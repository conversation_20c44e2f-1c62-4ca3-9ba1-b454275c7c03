# Supabase Integration Testing Guide

This guide provides comprehensive testing procedures for the Supabase integration in the Cotiju.app application.

## Prerequisites

1. Ensure Supabase project is set up and running
2. All migrations have been applied
3. Environment variables are properly configured
4. Application is running in development mode

## Testing Checklist

### 1. Authentication System ✅

#### User Registration
- [ ] Navigate to `/login`
- [ ] Switch to "Criar Conta" tab
- [ ] Fill in valid user details
- [ ] Submit form and verify success message
- [ ] Check email for confirmation link
- [ ] Verify user profile is created in database

#### User Login
- [ ] Navigate to `/login`
- [ ] Enter valid credentials
- [ ] Verify successful login and redirect
- [ ] Check that user session persists on page refresh
- [ ] Verify AuthStatus component shows user info

#### User Logout
- [ ] Click on user avatar in header
- [ ] Select "Sair" from dropdown
- [ ] Verify successful logout
- [ ] Check that protected routes redirect to login

#### Password Validation
- [ ] Test password requirements (minimum 6 characters)
- [ ] Test password confirmation matching
- [ ] Verify error messages display correctly

### 2. Profile Management ✅

#### Profile Display
- [ ] Navigate to `/perfil` while logged in
- [ ] Verify profile information displays correctly
- [ ] Check avatar, display name, and member since date

#### Profile Editing
- [ ] Click edit button on profile page
- [ ] Update display name
- [ ] Upload new avatar image
- [ ] Save changes and verify updates

#### Profile Real-time Updates
- [ ] Open profile in two browser tabs
- [ ] Update profile in one tab
- [ ] Verify changes appear in the other tab

### 3. Moto Taxi Driver System ✅

#### Driver Listing
- [ ] Navigate to `/moto-taxi`
- [ ] Verify drivers load from Supabase
- [ ] Test filtering by vehicle type (motorcycle/motorrete)
- [ ] Test availability filter
- [ ] Test PIX acceptance filter
- [ ] Test English language filter
- [ ] Test search functionality

#### Real-time Driver Updates
- [ ] Open moto-taxi page in two browser tabs
- [ ] Simulate driver availability change in database
- [ ] Verify updates appear in real-time

#### Driver Contact
- [ ] Click WhatsApp button on driver card
- [ ] Verify correct WhatsApp link opens
- [ ] Click phone button and verify tel: link

### 4. Photo Management System ✅

#### Photo Upload
- [ ] Navigate to `/perfil` while logged in
- [ ] Click "Enviar Foto" button
- [ ] Select valid image file
- [ ] Add caption (minimum 10 characters)
- [ ] Submit and verify upload success
- [ ] Check photo appears in user's photo grid

#### Photo Display
- [ ] Navigate to home page
- [ ] Verify featured photos carousel loads
- [ ] Test carousel navigation and autoplay
- [ ] Verify photo metadata displays correctly

#### Photo Likes
- [ ] Click heart button on photo
- [ ] Verify like count increases
- [ ] Click again to unlike
- [ ] Verify like count decreases
- [ ] Test like functionality while not logged in

#### Photo Real-time Updates
- [ ] Open home page in two browser tabs
- [ ] Like/unlike photos in one tab
- [ ] Verify changes appear in the other tab

### 5. Beach Locations System ✅

#### Location Data
- [ ] Verify beach locations are loaded from database
- [ ] Check that all location details display correctly
- [ ] Test location search functionality
- [ ] Verify coordinates and amenities display

#### Location Detail Pages
- [ ] Navigate to individual location pages
- [ ] Verify all location information displays
- [ ] Test "How to Get" and tips sections

### 6. Real-time Features ✅

#### Connection Status
- [ ] Check header shows "Online" when connected
- [ ] Simulate network disconnection
- [ ] Verify status changes to "Offline"
- [ ] Reconnect and verify status updates

#### Real-time Subscriptions
- [ ] Open application in multiple browser tabs
- [ ] Make changes in one tab (like photos, update profile)
- [ ] Verify changes appear in other tabs without refresh

### 7. Security Testing ✅

#### Row Level Security
- [ ] Create two user accounts
- [ ] Verify users can only see their own photos in profile
- [ ] Verify users cannot edit other users' profiles
- [ ] Test that unauthenticated users cannot access protected data

#### Data Validation
- [ ] Test form validation on all input fields
- [ ] Verify file upload restrictions (size, type)
- [ ] Test SQL injection prevention
- [ ] Verify XSS protection

#### Authentication Security
- [ ] Test session persistence
- [ ] Verify JWT token handling
- [ ] Test automatic token refresh
- [ ] Verify secure logout

### 8. Error Handling ✅

#### Network Errors
- [ ] Simulate network disconnection during operations
- [ ] Verify appropriate error messages display
- [ ] Test retry mechanisms

#### Database Errors
- [ ] Test behavior when database is unavailable
- [ ] Verify graceful degradation
- [ ] Check error logging

#### Validation Errors
- [ ] Test all form validation scenarios
- [ ] Verify error messages are user-friendly
- [ ] Test error state recovery

### 9. Performance Testing ✅

#### Loading Performance
- [ ] Test initial page load times
- [ ] Verify lazy loading of images
- [ ] Check query optimization

#### Real-time Performance
- [ ] Test with multiple concurrent users
- [ ] Verify real-time updates don't cause performance issues
- [ ] Check memory usage over time

### 10. Mobile Responsiveness ✅

#### Mobile Testing
- [ ] Test all features on mobile devices
- [ ] Verify touch interactions work correctly
- [ ] Test responsive design breakpoints
- [ ] Verify mobile-specific UI elements

## Common Issues and Solutions

### Authentication Issues
- **Problem**: User not redirected after login
- **Solution**: Check redirect logic in AuthContext
- **Problem**: Session not persisting
- **Solution**: Verify localStorage configuration

### Real-time Issues
- **Problem**: Real-time updates not working
- **Solution**: Check Supabase realtime configuration and subscriptions
- **Problem**: Multiple subscriptions causing performance issues
- **Solution**: Ensure proper cleanup in useEffect hooks

### Database Issues
- **Problem**: RLS policies blocking legitimate access
- **Solution**: Review and update security policies
- **Problem**: Query performance issues
- **Solution**: Add appropriate database indexes

## Deployment Testing

### Pre-deployment Checklist
- [ ] All tests pass in development
- [ ] Environment variables configured for production
- [ ] Database migrations applied to production
- [ ] SSL certificates configured
- [ ] CDN configured for static assets

### Post-deployment Verification
- [ ] All authentication flows work in production
- [ ] Real-time features function correctly
- [ ] File uploads work with production storage
- [ ] Performance meets requirements
- [ ] Security policies are enforced

## Monitoring and Maintenance

### Ongoing Monitoring
- [ ] Set up error tracking (e.g., Sentry)
- [ ] Monitor database performance
- [ ] Track real-time connection health
- [ ] Monitor storage usage

### Regular Maintenance
- [ ] Review and update security policies
- [ ] Optimize database queries
- [ ] Clean up unused files in storage
- [ ] Update dependencies regularly

## Conclusion

This comprehensive testing guide ensures that all Supabase integrations work correctly and securely. Regular testing and monitoring will help maintain the application's reliability and performance.

For any issues encountered during testing, refer to the Supabase documentation and the application's error logs for troubleshooting guidance.
