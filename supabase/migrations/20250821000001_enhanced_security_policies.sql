-- Enhanced security policies for all tables

-- ============================================================================
-- PROFILES TABLE POLICIES
-- ============================================================================

-- Drop existing policies to recreate with better security
drop policy if exists "Profiles are viewable by everyone" on public.profiles;
drop policy if exists "Users can update own profile" on public.profiles;
drop policy if exists "Users can insert own profile" on public.profiles;

-- Enhanced profile policies
create policy "Public can view profiles"
  on public.profiles for select using (true);

create policy "Users can insert own profile"
  on public.profiles for insert to authenticated 
  with check (auth.uid() = id);

create policy "Users can update own profile"
  on public.profiles for update to authenticated 
  using (auth.uid() = id)
  with check (auth.uid() = id);

create policy "Users can delete own profile"
  on public.profiles for delete to authenticated 
  using (auth.uid() = id);

-- ============================================================================
-- USER PHOTOS TABLE POLICIES
-- ============================================================================

-- Drop existing policies to recreate with better security
drop policy if exists "Public can view approved photos" on public.user_photos;
drop policy if exists "Users can insert own photos" on public.user_photos;
drop policy if exists "Users can update own photos" on public.user_photos;
drop policy if exists "Users can delete own photos" on public.user_photos;

-- Enhanced photo policies
create policy "Public can view approved photos"
  on public.user_photos for select 
  using (is_approved = true or auth.uid() = user_id);

create policy "Authenticated users can insert own photos"
  on public.user_photos for insert to authenticated 
  with check (auth.uid() = user_id and is_approved = false);

create policy "Users can update own photos"
  on public.user_photos for update to authenticated 
  using (auth.uid() = user_id)
  with check (auth.uid() = user_id);

create policy "Users can delete own photos"
  on public.user_photos for delete to authenticated 
  using (auth.uid() = user_id);

-- ============================================================================
-- PHOTO LIKES TABLE POLICIES
-- ============================================================================

-- Drop existing policies to recreate with better security
drop policy if exists "Anyone can read likes" on public.photo_likes;
drop policy if exists "Users can like" on public.photo_likes;
drop policy if exists "Users can unlike own like" on public.photo_likes;

-- Enhanced photo likes policies
create policy "Anyone can read likes"
  on public.photo_likes for select using (true);

create policy "Authenticated users can like photos"
  on public.photo_likes for insert to authenticated 
  with check (auth.uid() = user_id);

create policy "Users can unlike own likes"
  on public.photo_likes for delete to authenticated 
  using (auth.uid() = user_id);

-- Prevent duplicate likes
create unique index if not exists photo_likes_user_photo_unique 
  on public.photo_likes(photo_id, user_id);

-- ============================================================================
-- MOTO TAXI DRIVERS TABLE POLICIES
-- ============================================================================

-- Drop existing policies to recreate with better security
drop policy if exists "Public can view drivers" on public.moto_taxi_drivers;

-- Enhanced moto taxi driver policies
create policy "Public can view active drivers"
  on public.moto_taxi_drivers for select 
  using (is_available = true or is_verified = true);

-- Only allow admins to modify driver data (for now, no admin role system)
-- In production, you'd want to add an admin role system

-- ============================================================================
-- BEACH LOCATIONS TABLE POLICIES
-- ============================================================================

-- Enhanced beach location policies (already created in previous migration)
-- Just ensure they're properly set

-- ============================================================================
-- STORAGE POLICIES ENHANCEMENT
-- ============================================================================

-- Drop existing storage policies to recreate with better security
drop policy if exists "Public can view user photos" on storage.objects;
drop policy if exists "Users can upload to their folder" on storage.objects;
drop policy if exists "Users can update their own files" on storage.objects;
drop policy if exists "Users can delete their own files" on storage.objects;

-- Enhanced storage policies
create policy "Public can view user photos"
  on storage.objects for select 
  using (bucket_id = 'user-photos');

create policy "Authenticated users can upload to their folder"
  on storage.objects for insert to authenticated 
  with check (
    bucket_id = 'user-photos' 
    and auth.uid()::text = (storage.foldername(name))[1]
    and (storage.extension(name)) in ('jpg', 'jpeg', 'png', 'webp', 'gif')
    and (storage.size(name)) < 10485760 -- 10MB limit
  );

create policy "Users can update their own files"
  on storage.objects for update to authenticated 
  using (
    bucket_id = 'user-photos' 
    and auth.uid()::text = (storage.foldername(name))[1]
  )
  with check (
    bucket_id = 'user-photos' 
    and auth.uid()::text = (storage.foldername(name))[1]
  );

create policy "Users can delete their own files"
  on storage.objects for delete to authenticated 
  using (
    bucket_id = 'user-photos' 
    and auth.uid()::text = (storage.foldername(name))[1]
  );

-- ============================================================================
-- ADDITIONAL SECURITY FUNCTIONS
-- ============================================================================

-- Function to check if user is admin (placeholder for future admin system)
create or replace function public.is_admin(user_id uuid)
returns boolean as $$
begin
  -- For now, return false. In production, implement proper admin role checking
  return false;
end;
$$ language plpgsql security definer;

-- Function to sanitize user input
create or replace function public.sanitize_text(input_text text)
returns text as $$
begin
  -- Basic sanitization - remove potentially harmful characters
  return regexp_replace(
    regexp_replace(input_text, '[<>]', '', 'g'),
    '\s+', ' ', 'g'
  );
end;
$$ language plpgsql;

-- ============================================================================
-- AUDIT TRIGGERS (Optional - for production monitoring)
-- ============================================================================

-- Create audit log table
create table if not exists public.audit_log (
  id uuid primary key default gen_random_uuid(),
  table_name text not null,
  operation text not null,
  user_id uuid references auth.users(id),
  old_data jsonb,
  new_data jsonb,
  created_at timestamptz default now()
);

alter table public.audit_log enable row level security;

-- Only admins can view audit logs
create policy "Only admins can view audit logs"
  on public.audit_log for select 
  using (public.is_admin(auth.uid()));

-- Function to log changes
create or replace function public.audit_trigger()
returns trigger as $$
begin
  if TG_OP = 'DELETE' then
    insert into public.audit_log (table_name, operation, user_id, old_data)
    values (TG_TABLE_NAME, TG_OP, auth.uid(), to_jsonb(OLD));
    return OLD;
  elsif TG_OP = 'UPDATE' then
    insert into public.audit_log (table_name, operation, user_id, old_data, new_data)
    values (TG_TABLE_NAME, TG_OP, auth.uid(), to_jsonb(OLD), to_jsonb(NEW));
    return NEW;
  elsif TG_OP = 'INSERT' then
    insert into public.audit_log (table_name, operation, user_id, new_data)
    values (TG_TABLE_NAME, TG_OP, auth.uid(), to_jsonb(NEW));
    return NEW;
  end if;
  return null;
end;
$$ language plpgsql security definer;

-- Add audit triggers to sensitive tables (commented out for now)
-- drop trigger if exists audit_profiles on public.profiles;
-- create trigger audit_profiles
--   after insert or update or delete on public.profiles
--   for each row execute function public.audit_trigger();

-- ============================================================================
-- RATE LIMITING (Basic implementation)
-- ============================================================================

-- Create rate limiting table
create table if not exists public.rate_limits (
  id uuid primary key default gen_random_uuid(),
  user_id uuid references auth.users(id),
  action text not null,
  count integer default 1,
  window_start timestamptz default now(),
  created_at timestamptz default now()
);

alter table public.rate_limits enable row level security;

-- Users can only see their own rate limits
create policy "Users can view own rate limits"
  on public.rate_limits for select to authenticated
  using (auth.uid() = user_id);

-- Function to check rate limits
create or replace function public.check_rate_limit(
  action_name text,
  max_requests integer default 10,
  window_minutes integer default 60
)
returns boolean as $$
declare
  current_count integer;
begin
  -- Clean up old entries
  delete from public.rate_limits 
  where window_start < now() - interval '1 hour' * window_minutes / 60;
  
  -- Get current count for this user and action
  select coalesce(sum(count), 0) into current_count
  from public.rate_limits
  where user_id = auth.uid()
    and action = action_name
    and window_start > now() - interval '1 minute' * window_minutes;
  
  -- If under limit, increment counter
  if current_count < max_requests then
    insert into public.rate_limits (user_id, action, count)
    values (auth.uid(), action_name, 1)
    on conflict (user_id, action) do update set
      count = rate_limits.count + 1,
      window_start = case 
        when rate_limits.window_start < now() - interval '1 minute' * window_minutes
        then now()
        else rate_limits.window_start
      end;
    return true;
  end if;
  
  return false;
end;
$$ language plpgsql security definer;
