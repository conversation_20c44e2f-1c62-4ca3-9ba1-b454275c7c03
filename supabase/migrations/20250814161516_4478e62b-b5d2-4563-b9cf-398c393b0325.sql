-- Moto Taxi Drivers table
create table if not exists public.moto_taxi_drivers (
  id uuid primary key default gen_random_uuid(),
  name text not null,
  phone text not null,
  whatsapp text,
  vehicle_type text check (vehicle_type in ('motorcycle','motorrete')) not null,
  vehicle_model text,
  license_plate text,
  passenger_capacity integer default 1,
  photo_url text,
  areas_covered text[],
  languages text[] default array['pt'],
  years_experience integer,
  rating numeric(3,2) default 0,
  total_rides integer default 0,
  total_reviews integer default 0,
  price_range text,
  available_hours text,
  accepts_pix boolean default true,
  accepts_cash boolean default true,
  is_verified boolean default false,
  is_available boolean default true,
  notes text,
  created_at timestamptz default now(),
  updated_at timestamptz default now()
);

alter table public.moto_taxi_drivers enable row level security;

drop policy if exists "Public can view drivers" on public.moto_taxi_drivers;

create policy "Public can view drivers"
  on public.moto_taxi_drivers for select using (true);