-- Profiles table and trigger
create table if not exists public.profiles (
  id uuid primary key references auth.users(id) on delete cascade,
  display_name text,
  avatar_url text,
  member_since timestamptz not null default now()
);

alter table public.profiles enable row level security;

create policy "Profiles are viewable by everyone"
  on public.profiles for select using (true);

create policy "Users can update own profile"
  on public.profiles for update to authenticated using (auth.uid() = id);

create policy "Users can insert own profile"
  on public.profiles for insert to authenticated with check (auth.uid() = id);

-- Trigger to insert profile on signup
create or replace function public.handle_new_user()
returns trigger
language plpgsql
security definer set search_path = public
as $$
begin
  insert into public.profiles (id, display_name, avatar_url)
  values (new.id, coalesce(new.raw_user_meta_data->>'name', 'Usuário'), new.raw_user_meta_data->>'avatar_url')
  on conflict (id) do nothing;
  return new;
end;
$$;

drop trigger if exists on_auth_user_created on auth.users;
create trigger on_auth_user_created
  after insert on auth.users
  for each row execute procedure public.handle_new_user();

-- Moto Taxi Drivers table
create table if not exists public.moto_taxi_drivers (
  id uuid primary key default gen_random_uuid(),
  name text not null,
  phone text not null,
  whatsapp text,
  vehicle_type text check (vehicle_type in ('motorcycle','motorrete')) not null,
  vehicle_model text,
  license_plate text,
  passenger_capacity integer default 1,
  photo_url text,
  areas_covered text[],
  languages text[] default array['pt'],
  years_experience integer,
  rating numeric(3,2) default 0,
  total_rides integer default 0,
  total_reviews integer default 0,
  price_range text,
  available_hours text,
  accepts_pix boolean default true,
  accepts_cash boolean default true,
  is_verified boolean default false,
  is_available boolean default true,
  notes text,
  created_at timestamptz default now(),
  updated_at timestamptz default now()
);

alter table public.moto_taxi_drivers enable row level security;

create policy "Public can view drivers"
  on public.moto_taxi_drivers for select using (true);

-- Seed sample drivers (16 entries)
insert into public.moto_taxi_drivers (name, phone, whatsapp, vehicle_type, passenger_capacity, years_experience, areas_covered, price_range, available_hours, accepts_pix, accepts_cash, rating, total_reviews, is_available, languages)
values
('Carlos Monteiro','91987654321','91987654321','motorcycle',1,5,ARRAY['Todas as praias','Centro'],'R$ 5-10','06:00-22:00',true,true,4.8,23,true,ARRAY['pt']),
('José Ferreira','91988776655','91988776655','motorrete',6,8,ARRAY['Praia do Amor','Flexeira','Farol'],'R$ 10-20','07:00-20:00',true,false,4.9,45,true,ARRAY['pt']),
('Roberto Silva','91987665544','91987665544','motorcycle',1,3,ARRAY['Vai-Quem-Quer','Centro'],'R$ 5-8','08:00-18:00',false,true,4.6,15,false,ARRAY['pt']),
('Paulo Santos','91988990011','91988990011','motorrete',4,10,ARRAY['Todas as áreas'],'R$ 15-25','06:00-23:00',true,true,5.0,67,true,ARRAY['pt','en']),
('Miguel Costa','91987223344','91987223344','motorcycle',1,1,ARRAY['Praia do Amor','Centro'],'R$ 5-10','09:00-19:00',true,true,0.0,0,true,ARRAY['pt']),
('André Lima','91988334455','91988334455','motorcycle',1,7,ARRAY['Flexeira','Farol','Vai-Quem-Quer'],'R$ 8-12','07:00-21:00',true,false,4.7,31,true,ARRAY['pt']),
('Francisco Oliveira','91987556677','91987556677','motorrete',6,12,ARRAY['Todas as praias','Ruínas','Centro'],'R$ 12-20','06:00-22:00',true,true,4.9,89,false,ARRAY['pt']),
('João Pedro','91988445566','91988445566','motorcycle',1,4,ARRAY['Centro','Terminal'],'R$ 5-8','10:00-18:00',false,true,4.5,12,true,ARRAY['pt']),
('Ana Beatriz','91988112233','91988112233','motorcycle',1,6,ARRAY['Farol','Flexeira'],'R$ 7-12','07:00-19:00',true,true,4.7,28,true,ARRAY['pt','en']),
('Marcos Paulo','91988223344','91988223344','motorrete',6,9,ARRAY['Vai-Quem-Quer','Amor'],'R$ 12-22','08:00-21:00',true,true,4.8,54,true,ARRAY['pt']),
('Diego Souza','91988335544','91988335544','motorcycle',1,2,ARRAY['Centro'],'R$ 5-9','08:00-17:00',true,true,4.2,8,true,ARRAY['pt']),
('Pedro Henrique','91988446655','91988446655','motorrete',6,11,ARRAY['Todas as áreas'],'R$ 14-24','06:00-22:00',true,false,4.9,72,true,ARRAY['pt']),
('Rafael Lima','91988557766','91988557766','motorcycle',1,3,ARRAY['Amor','Flexeira'],'R$ 6-10','09:00-18:00',true,true,4.3,10,false,ARRAY['pt']),
('Felipe Alves','91988668877','91988668877','motorrete',5,5,ARRAY['Centro','Terminal'],'R$ 12-18','07:00-20:00',true,true,4.5,20,true,ARRAY['pt','en']),
('Lucas Gabriel','91988779988','91988779988','motorcycle',1,7,ARRAY['Farol','Vai-Quem-Quer'],'R$ 8-12','06:00-20:00',true,true,4.6,33,true,ARRAY['pt']),
('Bruno Carvalho','91988880011','91988880011','motorrete',6,6,ARRAY['Flexeira','Centro'],'R$ 13-21','08:00-22:00',true,true,4.4,18,true,ARRAY['pt'])
on conflict (name, phone) do nothing;

-- User photos and likes
create table if not exists public.user_photos (
  id uuid primary key default gen_random_uuid(),
  user_id uuid not null references auth.users(id) on delete cascade,
  image_url text not null,
  thumbnail_url text,
  caption text,
  location_id uuid,
  location_name text,
  tags text[],
  likes_count integer default 0,
  is_featured boolean default false,
  is_approved boolean default false,
  created_at timestamptz default now(),
  updated_at timestamptz default now()
);

alter table public.user_photos enable row level security;

create policy "Public can view approved photos"
  on public.user_photos for select using (is_approved = true or auth.uid() = user_id);

create policy "Users can insert own photos"
  on public.user_photos for insert to authenticated with check (auth.uid() = user_id);

create policy "Users can update own photos"
  on public.user_photos for update to authenticated using (auth.uid() = user_id);

create policy "Users can delete own photos"
  on public.user_photos for delete to authenticated using (auth.uid() = user_id);

create table if not exists public.photo_likes (
  id uuid primary key default gen_random_uuid(),
  photo_id uuid not null references public.user_photos(id) on delete cascade,
  user_id uuid not null references auth.users(id) on delete cascade,
  created_at timestamptz default now(),
  unique(photo_id, user_id)
);

alter table public.photo_likes enable row level security;

create policy "Anyone can read likes"
  on public.photo_likes for select using (true);

create policy "Users can like"
  on public.photo_likes for insert to authenticated with check (auth.uid() = user_id);

create policy "Users can unlike own like"
  on public.photo_likes for delete to authenticated using (auth.uid() = user_id);

-- Storage bucket for user photos
insert into storage.buckets (id, name, public)
values ('user-photos','user-photos', true)
on conflict (id) do nothing;

-- Storage policies
create policy "Public can view user photos"
  on storage.objects for select using (bucket_id = 'user-photos');

create policy "Users can upload to their folder"
  on storage.objects for insert to authenticated with check (
    bucket_id = 'user-photos' and auth.uid()::text = (storage.foldername(name))[1]
  );

create policy "Users can update their own files"
  on storage.objects for update to authenticated using (
    bucket_id = 'user-photos' and auth.uid()::text = (storage.foldername(name))[1]
  );

create policy "Users can delete their own files"
  on storage.objects for delete to authenticated using (
    bucket_id = 'user-photos' and auth.uid()::text = (storage.foldername(name))[1]
  );