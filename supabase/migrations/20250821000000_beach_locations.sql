-- Beach locations table
create table if not exists public.beach_locations (
  id uuid primary key default gen_random_uuid(),
  slug text unique not null,
  name text not null,
  description text,
  full_description text,
  coordinates text,
  latitude numeric(10, 8),
  longitude numeric(11, 8),
  amenities text[],
  how_to_get text,
  tips text[],
  is_featured boolean default false,
  is_active boolean default true,
  created_at timestamptz default now(),
  updated_at timestamptz default now()
);

alter table public.beach_locations enable row level security;

-- Public can view active locations
create policy "Public can view active locations"
  on public.beach_locations for select using (is_active = true);

-- Insert beach locations data
insert into public.beach_locations (
  slug, name, description, full_description, coordinates, latitude, longitude, 
  amenities, how_to_get, tips, is_featured
) values
(
  'praia-do-farol',
  'Praia do Farol',
  'Principal praia da ilha com farol histórico',
  'A Praia do Farol é a principal praia de Cotijuba, conhecida por seu farol histórico que serve como ponto de referência para navegadores. Com excelente infraestrutura, oferece restaurantes locais, banheiros limpos e amplas áreas de sombra natural.',
  '1°12''18"S 48°36''54"W',
  -1.205,
  -48.615,
  ARRAY['Restaurantes', 'Banheiros', 'Sombra natural'],
  'Localizada próximo ao porto principal da ilha, é facilmente acessível a pé ou de moto-táxi.',
  ARRAY['Melhor horário: manhã e final da tarde', 'Ideal para famílias', 'Tem estrutura para refeições'],
  true
),
(
  'praia-da-saudade',
  'Praia da Saudade',
  'Praia tranquila ideal para famílias',
  'Um refúgio de paz e tranquilidade, a Praia da Saudade é perfeita para famílias que buscam um ambiente mais reservado. Possui acesso a água doce natural e permite camping.',
  '1°13''05"S 48°37''41"W',
  -1.2181,
  -48.6281,
  ARRAY['Água doce', 'Área de camping', 'Trilhas'],
  'Acesso por trilha de aproximadamente 15 minutos a partir do centro da ilha.',
  ARRAY['Leve repelente', 'Ideal para camping', 'Água doce disponível'],
  true
),
(
  'praia-do-amor',
  'Praia do Amor',
  'Praia romântica com águas calmas',
  'Conhecida por seu ambiente romântico e pôr do sol espetacular, a Praia do Amor oferece águas calmas e rasas, perfeitas para relaxar. É um local mais reservado, ideal para casais.',
  '1°12''32"S 48°37''55"W',
  -1.2089,
  -48.6319,
  ARRAY['Privacidade', 'Pôr do sol', 'Águas rasas'],
  'Acesso por trilha curta de 10 minutos ou moto-táxi.',
  ARRAY['Melhor pôr do sol da ilha', 'Águas rasas e calmas', 'Ambiente romântico'],
  true
),
(
  'praia-da-flexeira',
  'Praia da Flexeira',
  'Praia com vegetação nativa preservada',
  'A Praia da Flexeira é um santuário natural com vegetação nativa preservada. Oferece trilhas ecológicas e é um dos melhores pontos para observação de aves na ilha.',
  '1°13''12"S 48°36''36"W',
  -1.22,
  -48.61,
  ARRAY['Natureza preservada', 'Trilhas ecológicas', 'Observação de aves'],
  'Acesso por trilha ecológica de 20 minutos, ideal para caminhadas.',
  ARRAY['Leve câmera para fotos', 'Ótima para trilhas', 'Rica em vida selvagem'],
  true
);

-- Create indexes for better performance
create index if not exists beach_locations_slug_idx on public.beach_locations(slug);
create index if not exists beach_locations_is_active_idx on public.beach_locations(is_active);
create index if not exists beach_locations_is_featured_idx on public.beach_locations(is_featured);
create index if not exists beach_locations_location_idx on public.beach_locations(latitude, longitude);

-- Function to update updated_at timestamp
create or replace function public.update_updated_at_column()
returns trigger as $$
begin
  new.updated_at = now();
  return new;
end;
$$ language plpgsql;

-- Trigger to automatically update updated_at
drop trigger if exists update_beach_locations_updated_at on public.beach_locations;
create trigger update_beach_locations_updated_at
  before update on public.beach_locations
  for each row execute procedure public.update_updated_at_column();
