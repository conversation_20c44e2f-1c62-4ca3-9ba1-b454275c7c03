// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://exxscdqyluftxhtxdofq.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImV4eHNjZHF5bHVmdHhodHhkb2ZxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTQ4NzEwMzksImV4cCI6MjA3MDQ0NzAzOX0.2wsYufp3xqO1gUY08fT5VhXxH3z4X1Ihpm6C1IPvetk";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    storage: localStorage,
    persistSession: true,
    autoRefreshToken: true,
  }
});