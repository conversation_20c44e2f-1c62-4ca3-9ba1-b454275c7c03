import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { AuthProvider } from "@/contexts/AuthContext";
import { RealtimeProvider } from "@/contexts/RealtimeContext";
import Index from "./pages/Index";
import Transport from "./pages/Transport";
import Weather from "./pages/Weather";
import Map from "./pages/Map";
import Login from "./pages/Login";

import MotoTaxi from "./pages/MotoTaxi";
import Profile from "./pages/Profile";
import LocationDetail from "./pages/LocationDetail";
import PlaceholderPage from "./pages/PlaceholderPage";
import NotFound from "./pages/NotFound";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <AuthProvider>
      <RealtimeProvider>
        <TooltipProvider>
          <Toaster />
          <Sonner />
          <BrowserRouter>
        <Routes>
          <Route path="/" element={<Index />} />
          <Route path="/moto-taxi" element={<MotoTaxi />} />
          <Route path="/perfil" element={<Profile />} />
          <Route path="/mapa" element={<Map />} />
          <Route path="/locais/:id" element={<LocationDetail />} />
          {/* Rotas antigas ainda acessíveis diretamente */}
          <Route path="/transport" element={<Transport />} />
          <Route path="/weather" element={<Weather />} />
          
          {/* Placeholder pages */}
          <Route path="/locations" element={
            <PlaceholderPage 
              title="Locais e Praias"
              description="Descubra as melhores praias e pontos turísticos de Cotijuba"
              comingSoonFeatures={[
                "Mapa interativo das praias",
                "Fotos e descrições detalhadas",
                "Avaliações de visitantes",
                "Informações sobre infraestrutura",
                "Condições de cada praia em tempo real"
              ]}
            />
          } />
          
          <Route path="/accommodations" element={
            <PlaceholderPage 
              title="Hospedagem"
              description="Encontre as melhores opções de hospedagem na ilha"
              comingSoonFeatures={[
                "Pousadas e hotéis disponíveis",
                "Preços e disponibilidade",
                "Fotos dos quartos e instalações",
                "Reservas online",
                "Avaliações de hóspedes"
              ]}
            />
          } />
          
          <Route path="/restaurants" element={
            <PlaceholderPage 
              title="Restaurantes"
              description="Saboreie a culinária local e regional"
              comingSoonFeatures={[
                "Cardápios e preços",
                "Especialidades regionais",
                "Horários de funcionamento",
                "Delivery disponível",
                "Avaliações e fotos dos pratos"
              ]}
            />
          } />
          
          <Route path="/activities" element={
            <PlaceholderPage 
              title="Atividades"
              description="Aventuras e experiências únicas na ilha"
              comingSoonFeatures={[
                "Passeios de barco",
                "Trilhas ecológicas",
                "Pesca esportiva",
                "Esportes aquáticos",
                "Reservas e agendamentos"
              ]}
            />
          } />
          
          <Route path="/events" element={
            <PlaceholderPage 
              title="Eventos"
              description="Acompanhe os eventos e festivais da ilha"
              comingSoonFeatures={[
                "Calendário de eventos",
                "Festivais culturais",
                "Shows e apresentações",
                "Eventos esportivos",
                "Informações sobre ingressos"
              ]}
            />
          } />
          
          <Route path="/taxi" element={
            <PlaceholderPage 
              title="Táxi"
              description="Serviços de transporte terrestre na ilha"
              comingSoonFeatures={[
                "Lista de taxistas credenciados",
                "Contatos diretos via WhatsApp",
                "Preços por destino",
                "Avaliações dos motoristas",
                "Agendamento de corridas"
              ]}
            />
          } />
          
          <Route path="/emergency" element={
            <PlaceholderPage 
              title="Emergência"
              description="Contatos importantes para sua segurança"
              comingSoonFeatures={[
                "Números de emergência",
                "Localização de postos de saúde",
                "Contatos da polícia local",
                "Bombeiros e resgate",
                "Localização em tempo real"
              ]}
            />
          } />
          
          <Route path="/menu" element={
            <PlaceholderPage 
              title="Menu Principal"
              description="Acesso a todas as funcionalidades do app"
              comingSoonFeatures={[
                "Perfil do usuário",
                "Configurações do app",
                "Favoritos salvos",
                "Histórico de viagens",
                "Sugestões e feedback"
              ]}
            />
          } />
          
          <Route path="/profile" element={
            <PlaceholderPage title="Perfil" />
          } />
          
          <Route path="/settings" element={
            <PlaceholderPage title="Configurações" />
          } />
          
          <Route path="/about" element={
            <PlaceholderPage title="Sobre" />
          } />
          
          <Route path="/suggest" element={
            <PlaceholderPage title="Sugestões" />
          } />
          
          <Route path="/login" element={<Login />} />
          
          <Route path="/offline" element={
            <PlaceholderPage title="Modo Offline" />
          } />

          {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
          <Route path="*" element={<NotFound />} />
        </Routes>
      </BrowserRouter>
    </TooltipProvider>
    </RealtimeProvider>
    </AuthProvider>
  </QueryClientProvider>
);

export default App;
