import { supabase } from '@/integrations/supabase/client';
import { Tables, TablesInsert, TablesUpdate } from '@/integrations/supabase/types';

export type BeachLocation = Tables<'beach_locations'>;
export type BeachLocationInsert = TablesInsert<'beach_locations'>;
export type BeachLocationUpdate = TablesUpdate<'beach_locations'>;

export const beachLocationService = {
  // Get all active beach locations
  async getBeachLocations(): Promise<BeachLocation[]> {
    const { data, error } = await supabase
      .from('beach_locations')
      .select('*')
      .eq('is_active', true)
      .order('name');

    if (error) {
      throw new Error(`Failed to fetch beach locations: ${error.message}`);
    }

    return data || [];
  },

  // Get featured beach locations
  async getFeaturedBeachLocations(): Promise<BeachLocation[]> {
    const { data, error } = await supabase
      .from('beach_locations')
      .select('*')
      .eq('is_active', true)
      .eq('is_featured', true)
      .order('name');

    if (error) {
      throw new Error(`Failed to fetch featured beach locations: ${error.message}`);
    }

    return data || [];
  },

  // Get a single beach location by slug
  async getBeachLocationBySlug(slug: string): Promise<BeachLocation | null> {
    const { data, error } = await supabase
      .from('beach_locations')
      .select('*')
      .eq('slug', slug)
      .eq('is_active', true)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return null; // Location not found
      }
      throw new Error(`Failed to fetch beach location: ${error.message}`);
    }

    return data;
  },

  // Get a single beach location by ID
  async getBeachLocationById(id: string): Promise<BeachLocation | null> {
    const { data, error } = await supabase
      .from('beach_locations')
      .select('*')
      .eq('id', id)
      .eq('is_active', true)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return null; // Location not found
      }
      throw new Error(`Failed to fetch beach location: ${error.message}`);
    }

    return data;
  },

  // Create a new beach location (admin only)
  async createBeachLocation(location: BeachLocationInsert): Promise<BeachLocation> {
    const { data, error } = await supabase
      .from('beach_locations')
      .insert(location)
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to create beach location: ${error.message}`);
    }

    return data;
  },

  // Update a beach location (admin only)
  async updateBeachLocation(id: string, updates: BeachLocationUpdate): Promise<BeachLocation> {
    const { data, error } = await supabase
      .from('beach_locations')
      .update(updates)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to update beach location: ${error.message}`);
    }

    return data;
  },

  // Delete a beach location (admin only) - soft delete by setting is_active to false
  async deleteBeachLocation(id: string): Promise<void> {
    const { error } = await supabase
      .from('beach_locations')
      .update({ is_active: false })
      .eq('id', id);

    if (error) {
      throw new Error(`Failed to delete beach location: ${error.message}`);
    }
  },

  // Search beach locations by name or description
  async searchBeachLocations(query: string): Promise<BeachLocation[]> {
    const { data, error } = await supabase
      .from('beach_locations')
      .select('*')
      .eq('is_active', true)
      .or(`name.ilike.%${query}%,description.ilike.%${query}%,full_description.ilike.%${query}%`)
      .order('name');

    if (error) {
      throw new Error(`Failed to search beach locations: ${error.message}`);
    }

    return data || [];
  },

  // Get beach locations within a certain distance (simplified - would need PostGIS for real geo queries)
  async getNearbyBeachLocations(latitude: number, longitude: number, radiusKm: number = 10): Promise<BeachLocation[]> {
    // This is a simplified version - for production, you'd want to use PostGIS
    const { data, error } = await supabase
      .from('beach_locations')
      .select('*')
      .eq('is_active', true)
      .not('latitude', 'is', null)
      .not('longitude', 'is', null)
      .order('name');

    if (error) {
      throw new Error(`Failed to fetch nearby beach locations: ${error.message}`);
    }

    // Filter by distance client-side (not ideal for large datasets)
    const filtered = (data || []).filter(location => {
      if (!location.latitude || !location.longitude) return false;
      
      const distance = calculateDistance(
        latitude, longitude,
        location.latitude, location.longitude
      );
      
      return distance <= radiusKm;
    });

    return filtered;
  },

  // Subscribe to beach location changes (real-time)
  subscribeToBeachLocations(callback: (payload: any) => void) {
    return supabase
      .channel('beach_locations_changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'beach_locations'
        },
        callback
      )
      .subscribe();
  }
};

// Helper function to calculate distance between two points (Haversine formula)
function calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
  const R = 6371; // Earth's radius in kilometers
  const dLat = (lat2 - lat1) * Math.PI / 180;
  const dLon = (lon2 - lon1) * Math.PI / 180;
  const a = 
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * 
    Math.sin(dLon/2) * Math.sin(dLon/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  return R * c;
}
