import { supabase } from '@/integrations/supabase/client';
import { Tables, TablesInsert, TablesUpdate } from '@/integrations/supabase/types';

export type MotoTaxiDriver = Tables<'moto_taxi_drivers'>;
export type MotoTaxiDriverInsert = TablesInsert<'moto_taxi_drivers'>;
export type MotoTaxiDriverUpdate = TablesUpdate<'moto_taxi_drivers'>;

export interface DriverFilters {
  availableNow?: boolean;
  acceptsPix?: boolean;
  speaksEnglish?: boolean;
  searchQuery?: string;
  vehicleType?: 'motorcycle' | 'motorrete';
}

export const motoTaxiService = {
  // Get all drivers with optional filters
  async getDrivers(filters?: DriverFilters): Promise<MotoTaxiDriver[]> {
    let query = supabase
      .from('moto_taxi_drivers')
      .select('*')
      .order('rating', { ascending: false });

    // Apply filters
    if (filters?.availableNow) {
      query = query.eq('is_available', true);
    }

    if (filters?.acceptsPix) {
      query = query.eq('accepts_pix', true);
    }

    if (filters?.vehicleType) {
      query = query.eq('vehicle_type', filters.vehicleType);
    }

    if (filters?.speaksEnglish) {
      query = query.contains('languages', ['en']);
    }

    const { data, error } = await query;

    if (error) {
      throw new Error(`Failed to fetch drivers: ${error.message}`);
    }

    let drivers = data || [];

    // Apply search filter client-side for complex text search
    if (filters?.searchQuery) {
      const searchTerm = filters.searchQuery.toLowerCase();
      drivers = drivers.filter(driver => 
        driver.name.toLowerCase().includes(searchTerm) ||
        driver.areas_covered?.some(area => 
          area.toLowerCase().includes(searchTerm)
        )
      );
    }

    return drivers;
  },

  // Get a single driver by ID
  async getDriver(id: string): Promise<MotoTaxiDriver | null> {
    const { data, error } = await supabase
      .from('moto_taxi_drivers')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return null; // Driver not found
      }
      throw new Error(`Failed to fetch driver: ${error.message}`);
    }

    return data;
  },

  // Create a new driver (admin only)
  async createDriver(driver: MotoTaxiDriverInsert): Promise<MotoTaxiDriver> {
    const { data, error } = await supabase
      .from('moto_taxi_drivers')
      .insert(driver)
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to create driver: ${error.message}`);
    }

    return data;
  },

  // Update a driver (admin only)
  async updateDriver(id: string, updates: MotoTaxiDriverUpdate): Promise<MotoTaxiDriver> {
    const { data, error } = await supabase
      .from('moto_taxi_drivers')
      .update(updates)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to update driver: ${error.message}`);
    }

    return data;
  },

  // Delete a driver (admin only)
  async deleteDriver(id: string): Promise<void> {
    const { error } = await supabase
      .from('moto_taxi_drivers')
      .delete()
      .eq('id', id);

    if (error) {
      throw new Error(`Failed to delete driver: ${error.message}`);
    }
  },

  // Update driver availability
  async updateDriverAvailability(id: string, isAvailable: boolean): Promise<MotoTaxiDriver> {
    const { data, error } = await supabase
      .from('moto_taxi_drivers')
      .update({ 
        is_available: isAvailable,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to update driver availability: ${error.message}`);
    }

    return data;
  },

  // Subscribe to driver changes (real-time)
  subscribeToDrivers(callback: (payload: any) => void) {
    return supabase
      .channel('moto_taxi_drivers_changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'moto_taxi_drivers'
        },
        callback
      )
      .subscribe();
  },

  // Get drivers by vehicle type with real-time subscription
  subscribeToDriversByType(
    vehicleType: 'motorcycle' | 'motorrete',
    callback: (drivers: MotoTaxiDriver[]) => void
  ) {
    // Initial fetch
    this.getDrivers({ vehicleType }).then(callback);

    // Subscribe to changes
    return this.subscribeToDrivers(async () => {
      const drivers = await this.getDrivers({ vehicleType });
      callback(drivers);
    });
  }
};
