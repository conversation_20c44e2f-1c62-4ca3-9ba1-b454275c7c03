import React, { createContext, useContext, useEffect, useState } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/hooks/useAuth';
import { MOTO_TAXI_QUERY_KEYS } from '@/hooks/useMotoTaxi';
import { PHOTO_QUERY_KEYS } from '@/hooks/usePhotos';
import { BEACH_LOCATION_QUERY_KEYS } from '@/hooks/useBeachLocations';
import { PROFILE_QUERY_KEYS } from '@/hooks/useProfile';

interface RealtimeContextType {
  isConnected: boolean;
  connectionStatus: 'connecting' | 'connected' | 'disconnected' | 'error';
}

const RealtimeContext = createContext<RealtimeContextType | undefined>(undefined);

export const useRealtime = () => {
  const context = useContext(RealtimeContext);
  if (context === undefined) {
    throw new Error('useRealtime must be used within a RealtimeProvider');
  }
  return context;
};

interface RealtimeProviderProps {
  children: React.ReactNode;
}

export const RealtimeProvider: React.FC<RealtimeProviderProps> = ({ children }) => {
  const { user } = useAuth();
  const queryClient = useQueryClient();
  const [connectionStatus, setConnectionStatus] = useState<'connecting' | 'connected' | 'disconnected' | 'error'>('connecting');

  useEffect(() => {
    // Set up real-time subscriptions
    const subscriptions: any[] = [];

    // Moto taxi drivers subscription
    const driversSubscription = supabase
      .channel('moto_taxi_drivers_realtime')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'moto_taxi_drivers'
        },
        (payload) => {
          console.log('Driver change:', payload);
          // Invalidate all driver queries
          queryClient.invalidateQueries({
            queryKey: MOTO_TAXI_QUERY_KEYS.all
          });
        }
      )
      .subscribe((status) => {
        if (status === 'SUBSCRIBED') {
          console.log('Subscribed to driver changes');
        }
      });

    subscriptions.push(driversSubscription);

    // User photos subscription
    const photosSubscription = supabase
      .channel('user_photos_realtime')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'user_photos'
        },
        (payload) => {
          console.log('Photo change:', payload);
          // Invalidate photo queries
          queryClient.invalidateQueries({
            queryKey: PHOTO_QUERY_KEYS.all
          });
        }
      )
      .subscribe((status) => {
        if (status === 'SUBSCRIBED') {
          console.log('Subscribed to photo changes');
        }
      });

    subscriptions.push(photosSubscription);

    // Photo likes subscription
    const likesSubscription = supabase
      .channel('photo_likes_realtime')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'photo_likes'
        },
        (payload) => {
          console.log('Like change:', payload);
          // Invalidate photo queries to refresh like counts
          queryClient.invalidateQueries({
            queryKey: PHOTO_QUERY_KEYS.featured()
          });
        }
      )
      .subscribe((status) => {
        if (status === 'SUBSCRIBED') {
          console.log('Subscribed to like changes');
        }
      });

    subscriptions.push(likesSubscription);

    // Beach locations subscription
    const locationsSubscription = supabase
      .channel('beach_locations_realtime')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'beach_locations'
        },
        (payload) => {
          console.log('Location change:', payload);
          // Invalidate location queries
          queryClient.invalidateQueries({
            queryKey: BEACH_LOCATION_QUERY_KEYS.all
          });
        }
      )
      .subscribe((status) => {
        if (status === 'SUBSCRIBED') {
          console.log('Subscribed to location changes');
        }
      });

    subscriptions.push(locationsSubscription);

    // User profile subscription (only if user is logged in)
    let profileSubscription: any = null;
    if (user) {
      profileSubscription = supabase
        .channel(`profile_${user.id}_realtime`)
        .on(
          'postgres_changes',
          {
            event: '*',
            schema: 'public',
            table: 'profiles',
            filter: `id=eq.${user.id}`
          },
          (payload) => {
            console.log('Profile change:', payload);
            // Invalidate profile queries
            queryClient.invalidateQueries({
              queryKey: PROFILE_QUERY_KEYS.profile(user.id)
            });
          }
        )
        .subscribe((status) => {
          if (status === 'SUBSCRIBED') {
            console.log('Subscribed to profile changes');
          }
        });

      subscriptions.push(profileSubscription);
    }

    // Monitor connection status
    const checkConnection = () => {
      const status = supabase.realtime.channels.length > 0 ? 'connected' : 'disconnected';
      setConnectionStatus(status);
    };

    // Check connection status periodically
    const connectionInterval = setInterval(checkConnection, 5000);
    checkConnection(); // Initial check

    // Set connected status after a short delay
    setTimeout(() => {
      setConnectionStatus('connected');
    }, 1000);

    // Cleanup function
    return () => {
      clearInterval(connectionInterval);
      subscriptions.forEach(subscription => {
        if (subscription) {
          subscription.unsubscribe();
        }
      });
      setConnectionStatus('disconnected');
    };
  }, [user, queryClient]);

  // Handle connection errors
  useEffect(() => {
    const handleError = (error: any) => {
      console.error('Realtime error:', error);
      setConnectionStatus('error');
    };

    // Listen for realtime errors
    supabase.realtime.onError(handleError);

    return () => {
      // Cleanup error listener if possible
    };
  }, []);

  const value: RealtimeContextType = {
    isConnected: connectionStatus === 'connected',
    connectionStatus,
  };

  return (
    <RealtimeContext.Provider value={value}>
      {children}
    </RealtimeContext.Provider>
  );
};
