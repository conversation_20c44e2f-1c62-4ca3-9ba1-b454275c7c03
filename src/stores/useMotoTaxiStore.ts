import { create } from 'zustand';
import { MotoTaxiDriver } from '@/services/motoTaxiService';

// Re-export the driver type from the service for backward compatibility
export type Driver = MotoTaxiDriver;

// Mock data is now replaced by Supabase data
// The actual data is seeded in the migration files

export interface Filters {
  availableNow: boolean;
  acceptsPix: boolean;
  speaksEnglish: boolean;
  searchQuery: string;
}

interface MotoTaxiState {
  filters: Filters;
  setFilter: (name: keyof Filters, value: boolean | string) => void;
}

// Simplified store that only manages filters
// Driver data is now managed by React Query hooks
export const useMotoTaxiStore = create<MotoTaxiState>((set) => ({
  filters: { availableNow: false, acceptsPix: false, speaksEnglish: false, searchQuery: '' },
  setFilter: (name, value) => set((state) => ({ filters: { ...state.filters, [name]: value as never } })),
}));
