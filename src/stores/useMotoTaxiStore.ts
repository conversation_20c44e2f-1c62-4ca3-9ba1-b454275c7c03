import { create } from 'zustand';

export interface Driver {
  id: string;
  name: string;
  vehicle_type: 'motorcycle' | 'motorrete';
  phone: string;
  whatsapp: string;
  passenger_capacity?: number;
  years_experience: number;
  areas_covered: string[];
  price_range: string;
  available_hours: string;
  accepts_pix: boolean;
  accepts_cash: boolean;
  rating: number;
  total_reviews: number;
  is_available: boolean;
  photo_url: string | null;
  languages?: string[];
}

const sampleDrivers: Driver[] = [
  { id: '1', name: '<PERSON>', vehicle_type: 'motorcycle', phone: '91987654321', whatsapp: '91987654321', years_experience: 5, areas_covered: ['Todas as praias', 'Centro'], price_range: 'R$ 5-10', available_hours: '6:00 - 22:00', accepts_pix: true, accepts_cash: true, rating: 4.8, total_reviews: 23, is_available: true, photo_url: null },
  { id: '2', name: '<PERSON>', vehicle_type: 'motorrete', phone: '91988776655', whatsapp: '91988776655', passenger_capacity: 6, years_experience: 8, areas_covered: ['Praia do Amor', 'Flexeira', 'Farol'], price_range: 'R$ 10-20', available_hours: '7:00 - 20:00', accepts_pix: true, accepts_cash: false, rating: 4.9, total_reviews: 45, is_available: true, photo_url: null },
  { id: '3', name: 'Roberto Silva', vehicle_type: 'motorcycle', phone: '91987665544', whatsapp: '91987665544', years_experience: 3, areas_covered: ['Vai-Quem-Quer', 'Centro'], price_range: 'R$ 5-8', available_hours: '8:00 - 18:00', accepts_pix: false, accepts_cash: true, rating: 4.6, total_reviews: 15, is_available: false, photo_url: null },
  { id: '4', name: 'Paulo Santos', vehicle_type: 'motorrete', phone: '91988990011', whatsapp: '91988990011', passenger_capacity: 4, years_experience: 10, areas_covered: ['Todas as áreas'], price_range: 'R$ 15-25', available_hours: '6:00 - 23:00', accepts_pix: true, accepts_cash: true, rating: 5.0, total_reviews: 67, is_available: true, photo_url: null, languages: ['Português', 'Inglês'] },
  { id: '5', name: 'Miguel Costa', vehicle_type: 'motorcycle', phone: '91987223344', whatsapp: '91987223344', years_experience: 1, areas_covered: ['Praia do Amor', 'Centro'], price_range: 'R$ 5-10', available_hours: '9:00 - 19:00', accepts_pix: true, accepts_cash: true, rating: 0, total_reviews: 0, is_available: true, photo_url: null },
  { id: '6', name: 'André Lima', vehicle_type: 'motorcycle', phone: '91988334455', whatsapp: '91988334455', years_experience: 7, areas_covered: ['Flexeira', 'Farol', 'Vai-Quem-Quer'], price_range: 'R$ 8-12', available_hours: '7:00 - 21:00', accepts_pix: true, accepts_cash: false, rating: 4.7, total_reviews: 31, is_available: true, photo_url: null },
  { id: '7', name: 'Francisco Oliveira', vehicle_type: 'motorrete', phone: '91987556677', whatsapp: '91987556677', passenger_capacity: 6, years_experience: 12, areas_covered: ['Todas as praias', 'Ruínas', 'Centro'], price_range: 'R$ 12-20', available_hours: '6:00 - 22:00', accepts_pix: true, accepts_cash: true, rating: 4.9, total_reviews: 89, is_available: false, photo_url: null },
  { id: '8', name: 'João Pedro', vehicle_type: 'motorcycle', phone: '91988445566', whatsapp: '91988445566', years_experience: 4, areas_covered: ['Centro', 'Terminal'], price_range: 'R$ 5-8', available_hours: '10:00 - 18:00', accepts_pix: false, accepts_cash: true, rating: 4.5, total_reviews: 12, is_available: true, photo_url: null },
];

interface Filters {
  availableNow: boolean;
  acceptsPix: boolean;
  speaksEnglish: boolean;
  searchQuery: string;
}

interface MotoTaxiState {
  drivers: Driver[];
  filters: Filters;
  setFilter: (name: keyof Filters, value: boolean | string) => void;
  getFilteredDrivers: (vehicleType: Driver['vehicle_type']) => Driver[];
}

export const useMotoTaxiStore = create<MotoTaxiState>((set, get) => ({
  drivers: sampleDrivers,
  filters: { availableNow: false, acceptsPix: false, speaksEnglish: false, searchQuery: '' },
  setFilter: (name, value) => set((state) => ({ filters: { ...state.filters, [name]: value as never } })),
  getFilteredDrivers: (vehicleType) => {
    const { drivers, filters } = get();
    let filtered = drivers.filter((d) => d.vehicle_type === vehicleType);
    if (filters.availableNow) filtered = filtered.filter((d) => d.is_available);
    if (filters.acceptsPix) filtered = filtered.filter((d) => d.accepts_pix);
    if (filters.speaksEnglish) filtered = filtered.filter((d) => d.languages?.includes('Inglês'));
    if (filters.searchQuery) {
      const q = filters.searchQuery.toLowerCase();
      filtered = filtered.filter(
        (d) => d.name.toLowerCase().includes(q) || d.areas_covered.some((a) => a.toLowerCase().includes(q))
      );
    }
    return filtered.sort(() => Math.random() - 0.5);
  },
}));
