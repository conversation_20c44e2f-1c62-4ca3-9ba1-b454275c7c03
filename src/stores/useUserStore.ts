import { create } from 'zustand';
import { persist } from 'zustand/middleware';

// This store is now deprecated in favor of the AuthContext
// Keeping it for backward compatibility during migration
export interface AppUser {
  id: string;
  name: string;
  email: string;
  avatar_url?: string | null;
  member_since?: string;
  favorites?: string[];
}

interface UserState {
  // Favorites are now managed locally since they're not in the database yet
  favorites: string[];
  toggleFavorite: (itemId: string) => void;
}

export const useUserStore = create<UserState>()(
  persist(
    (set) => ({
      favorites: [],

      toggleFavorite: (itemId) =>
        set((state) => ({
          favorites: state.favorites.includes(itemId)
            ? state.favorites.filter((id) => id !== itemId)
            : [...state.favorites, itemId],
        })),
    }),
    { name: 'cotijuba-user-favorites' }
  )
);
