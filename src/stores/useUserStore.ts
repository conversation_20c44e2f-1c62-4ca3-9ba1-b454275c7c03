import { create } from 'zustand';
import { persist } from 'zustand/middleware';

export interface AppUser {
  id: string;
  name: string;
  email: string;
  avatar_url?: string | null;
  member_since?: string;
  favorites?: string[];
}

interface UserState {
  isLoggedIn: boolean;
  user: AppUser | null;
  login: (userData: AppUser) => void;
  logout: () => void;
  toggleFavorite: (itemId: string) => void;
}

export const useUserStore = create<UserState>()(
  persist(
    (set) => ({
      isLoggedIn: false,
      user: null,

      login: (userData) => set({ isLoggedIn: true, user: userData }),
      logout: () => set({ isLoggedIn: false, user: null }),

      toggleFavorite: (itemId) =>
        set((state) => ({
          user: state.user
            ? {
                ...state.user,
                favorites: state.user.favorites?.includes(itemId)
                  ? state.user.favorites.filter((id) => id !== itemId)
                  : [...(state.user.favorites || []), itemId],
              }
            : state.user,
        })),
    }),
    { name: 'cotijuba-user' }
  )
);
