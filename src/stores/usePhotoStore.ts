import { create } from 'zustand';

// This store is now deprecated in favor of React Query hooks
// Keeping it for backward compatibility during migration

export interface PhotoItem {
  id: string;
  image_url: string;
  caption: string;
  user_name: string;
  likes_count: number;
  is_liked: boolean;
}

export interface UserPhoto extends PhotoItem {
  uploaded_at?: string;
  status?: 'pending' | 'approved' | 'rejected';
  thumbnail_url?: string | null;
}

// Simplified store - most functionality moved to React Query hooks
interface PhotoStoreState {
  // Keep some local state for UI purposes if needed
  selectedPhotoId: string | null;
  setSelectedPhoto: (photoId: string | null) => void;
}

export const usePhotoStore = create<PhotoStoreState>((set) => ({
  selectedPhotoId: null,
  setSelectedPhoto: (photoId) => set({ selectedPhotoId: photoId }),
}));
