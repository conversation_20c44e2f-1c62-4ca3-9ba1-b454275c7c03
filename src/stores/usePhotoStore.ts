import { create } from 'zustand';
import { persist } from 'zustand/middleware';

export interface PhotoItem {
  id: string;
  image_url: string;
  caption: string;
  user_name: string;
  likes_count: number;
  is_liked: boolean;
}

const placeholderPhotos: PhotoItem[] = [
  {
    id: '1',
    image_url: 'https://images.unsplash.com/photo-1559827260-dc66d52bef19?w=800',
    caption: 'P<PERSON>r do sol na Praia do Amor',
    user_name: '<PERSON>',
    likes_count: 45,
    is_liked: false,
  },
  {
    id: '2',
    image_url: 'https://images.unsplash.com/photo-1519046904884-53103b34b206?w=800',
    caption: '<PERSON><PERSON><PERSON> <PERSON>-<PERSON>-<PERSON>',
    user_name: '<PERSON>',
    likes_count: 32,
    is_liked: false,
  },
  {
    id: '3',
    image_url: 'https://images.unsplash.com/photo-1507525428034-b723cf961d3e?w=800',
    caption: '<PERSON><PERSON><PERSON> c<PERSON> Cotijuba',
    user_name: '<PERSON>',
    likes_count: 67,
    is_liked: true,
  },
  {
    id: '4',
    image_url: 'https://images.unsplash.com/photo-1544551763-46a013bb70d5?w=800',
    caption: 'Marina de Cotijuba ao amanhecer',
    user_name: 'Ana Oliveira',
    likes_count: 89,
    is_liked: false,
  },
  {
    id: '5',
    image_url: 'https://images.unsplash.com/photo-1527004760902-e27c2c70dacf?w=800',
    caption: 'Coqueiros na Praia da Flexeira',
    user_name: 'Carlos Mendes',
    likes_count: 23,
    is_liked: false,
  },
];

export interface UserPhoto extends PhotoItem {
  uploaded_at?: string;
  status?: 'pending' | 'approved' | 'rejected';
  thumbnail_url?: string | null;
}

interface PhotoStoreState {
  userPhotos: UserPhoto[];
  featuredPhotos: PhotoItem[];
  likedPhotos: Set<string>;
  toggleLike: (photoId: string) => void;
  addUserPhoto: (photo: Omit<UserPhoto, 'id' | 'status' | 'uploaded_at'>) => void;
  removeUserPhoto: (photoId: string) => void;
}

export const usePhotoStore = create<PhotoStoreState>()(
  persist(
    (set) => ({
      userPhotos: [],
      featuredPhotos: placeholderPhotos,
      likedPhotos: new Set<string>(),

      toggleLike: (photoId: string) =>
        set((state) => {
          const newLiked = new Set(state.likedPhotos);
          if (newLiked.has(photoId)) newLiked.delete(photoId);
          else newLiked.add(photoId);

          const updatedPhotos = state.featuredPhotos.map((p) =>
            p.id === photoId
              ? {
                  ...p,
                  likes_count: newLiked.has(photoId)
                    ? p.likes_count + 1
                    : Math.max(0, p.likes_count - 1),
                  is_liked: newLiked.has(photoId),
                }
              : p
          );

          return { likedPhotos: newLiked, featuredPhotos: updatedPhotos };
        }),

      addUserPhoto: (photo) =>
        set((state) => ({
          userPhotos: [
            ...state.userPhotos,
            {
              ...photo,
              id: Date.now().toString(),
              status: 'pending',
              uploaded_at: new Date().toISOString(),
            },
          ],
        })),

      removeUserPhoto: (photoId) =>
        set((state) => ({
          userPhotos: state.userPhotos.filter((p) => p.id !== photoId),
        })),
    }),
    { name: 'cotijuba-photos' }
  )
);
