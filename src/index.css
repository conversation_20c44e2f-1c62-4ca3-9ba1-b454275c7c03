@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Cotijuba Tourism App Design System - Inspired by Brazilian coastal beauty */

@layer base {
  :root {
    /* Core ocean-inspired color palette */
    --background: 210 30% 98%;
    --foreground: 220 25% 15%;

    --card: 0 0% 100%;
    --card-foreground: 220 25% 15%;

    --popover: 0 0% 100%;
    --popover-foreground: 220 25% 15%;

    /* Ocean Blue Primary */
    --primary: 214 100% 40%;
    --primary-foreground: 0 0% 100%;
    --primary-hover: 214 100% 35%;

    /* Palm Green Secondary */
    --secondary: 145 70% 35%;
    --secondary-foreground: 0 0% 100%;
    --secondary-hover: 145 70% 30%;

    /* Neutral backgrounds */
    --muted: 210 20% 96%;
    --muted-foreground: 220 15% 45%;

    /* Sunset Orange Accent */
    --accent: 30 100% 50%;
    --accent-foreground: 0 0% 100%;
    --accent-hover: 30 100% 45%;

    --destructive: 0 75% 55%;
    --destructive-foreground: 0 0% 100%;

    --border: 220 20% 88%;
    --input: 220 20% 90%;
    --ring: 214 100% 40%;

    --radius: 0.75rem;

    /* Custom gradients */
    --gradient-ocean: linear-gradient(135deg, hsl(214 100% 40%), hsl(195 85% 45%));
    --gradient-sunset: linear-gradient(135deg, hsl(30 100% 50%), hsl(15 90% 55%));
    --gradient-palm: linear-gradient(135deg, hsl(145 70% 35%), hsl(160 60% 40%));
    --gradient-card: linear-gradient(145deg, hsl(0 0% 100%), hsl(210 15% 98%));

    /* Shadows */
    --shadow-ocean: 0 4px 20px hsl(214 100% 40% / 0.15);
    --shadow-card: 0 2px 8px hsl(220 25% 15% / 0.08);
    --shadow-hover: 0 8px 30px hsl(214 100% 40% / 0.25);

    /* Beach-themed spacing */
    --space-xs: 0.25rem;
    --space-sm: 0.5rem;
    --space-md: 1rem;
    --space-lg: 1.5rem;
    --space-xl: 2rem;
    --space-2xl: 3rem;

  }

  .dark {
    --background: 220 35% 8%;
    --foreground: 210 25% 95%;

    --card: 220 30% 10%;
    --card-foreground: 210 25% 95%;

    --popover: 220 30% 10%;
    --popover-foreground: 210 25% 95%;

    --primary: 214 100% 60%;
    --primary-foreground: 220 35% 8%;

    --secondary: 145 70% 45%;
    --secondary-foreground: 220 35% 8%;

    --muted: 220 25% 15%;
    --muted-foreground: 220 15% 65%;

    --accent: 30 100% 60%;
    --accent-foreground: 220 35% 8%;

    --destructive: 0 75% 65%;
    --destructive-foreground: 220 35% 8%;

    --border: 220 25% 20%;
    --input: 220 25% 18%;
    --ring: 214 100% 60%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-body;
    font-family: 'Inter', system-ui, -apple-system, sans-serif;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-display;
    font-family: 'Inter', system-ui, -apple-system, sans-serif;
  }
}

@layer components {
  /* Touch-friendly mobile optimizations */
  .touch-target {
    @apply min-h-[44px] min-w-[44px];
  }

  .card-ocean {
    @apply bg-gradient-to-br from-white to-slate-50 shadow-[0_2px_8px_hsl(220_25%_15%/0.08)] border border-slate-200/50;
  }

  .btn-ocean {
    @apply bg-gradient-to-r from-blue-600 to-blue-500 hover:from-blue-700 hover:to-blue-600 text-white shadow-[0_4px_20px_hsl(214_100%_40%/0.15)] hover:shadow-[0_8px_30px_hsl(214_100%_40%/0.25)] transition-all duration-200;
  }

  .btn-palm {
    @apply bg-gradient-to-r from-emerald-600 to-emerald-500 hover:from-emerald-700 hover:to-emerald-600 text-white shadow-[0_4px_20px_hsl(145_70%_35%/0.15)] hover:shadow-[0_8px_30px_hsl(145_70%_35%/0.25)] transition-all duration-200;
  }

  .btn-sunset {
    @apply bg-gradient-to-r from-orange-500 to-orange-400 hover:from-orange-600 hover:to-orange-500 text-white shadow-[0_4px_20px_hsl(30_100%_50%/0.15)] hover:shadow-[0_8px_30px_hsl(30_100%_50%/0.25)] transition-all duration-200;
  }

  .nav-bottom {
    @apply fixed bottom-0 left-0 right-0 bg-white/95 backdrop-blur-md border-t border-slate-200 safe-area-pb;
    z-index: 9999;
  }

  .widget-card {
    @apply card-ocean rounded-xl p-4 space-y-3;
  }

  .quick-link {
    @apply flex flex-col items-center justify-center p-3 sm:p-4 md:p-5 rounded-xl bg-gradient-to-br from-white to-slate-50 border border-slate-200/60 shadow-sm hover:shadow-md transition-all duration-200 touch-target;
  }
}

@layer utilities {
  /* Utilitário para respeitar a área segura (iOS/Android) no rodapé */
  .safe-area-pb {
    padding-bottom: max(var(--space-sm), env(safe-area-inset-bottom));
  }

  /* Touch target improvements for mobile */
  .touch-target {
    min-height: 44px;
    min-width: 44px;
  }

  /* Map container improvements */
  .map-container {
    touch-action: pan-x pan-y;
    z-index: 1;
  }

  /* Ensure Leaflet map doesn't override navigation */
  .leaflet-container {
    z-index: 1 !important;
  }

  .leaflet-control-container {
    z-index: 10 !important;
  }

  /* Line clamp utilities */
  .line-clamp-1 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }

  .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }

  .line-clamp-3 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }
}
