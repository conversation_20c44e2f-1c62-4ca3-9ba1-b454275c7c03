import { createRoot } from 'react-dom/client'
import App from './App.tsx'
import './index.css'

// Dev-only: unregister any service workers and clear caches to ensure HMR/live preview works
if (typeof window !== 'undefined' && 'serviceWorker' in navigator) {
  const isLocal = ['localhost', '127.0.0.1', '[::1]'].includes(window.location.hostname);
  if (isLocal) {
    navigator.serviceWorker.getRegistrations()
      .then((regs) => Promise.all(regs.map((r) => r.unregister())))
      .then(() => (window as any).caches?.keys())
      .then((keys: string[] | undefined) =>
        Promise.all((keys || []).map((k) => (window as any).caches.delete(k)))
      )
      .catch(() => {/* no-op */});
  }
}

createRoot(document.getElementById("root")!).render(<App />);
