import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { profileService, ProfileUpdate } from '@/services/profileService';
import { useAuth } from '@/hooks/useAuth';
import { useEffect } from 'react';

export const PROFILE_QUERY_KEYS = {
  all: ['profiles'] as const,
  profile: (userId: string) => [...PROFILE_QUERY_KEYS.all, userId] as const,
  allProfiles: () => [...PROFILE_QUERY_KEYS.all, 'list'] as const,
};

// Hook to get a profile
export const useProfile = (userId?: string) => {
  const { user } = useAuth();
  const targetUserId = userId || user?.id;

  return useQuery({
    queryKey: PROFILE_QUERY_KEYS.profile(targetUserId || ''),
    queryFn: () => profileService.getProfile(targetUserId!),
    enabled: !!targetUserId,
  });
};

// Hook to get current user's profile with real-time updates
export const useCurrentUserProfile = () => {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  const query = useQuery({
    queryKey: PROFILE_QUERY_KEYS.profile(user?.id || ''),
    queryFn: () => profileService.getProfile(user!.id),
    enabled: !!user,
  });

  // Subscribe to real-time updates for current user's profile
  useEffect(() => {
    if (!user) return;

    const subscription = profileService.subscribeToProfile(user.id, () => {
      queryClient.invalidateQueries({ 
        queryKey: PROFILE_QUERY_KEYS.profile(user.id) 
      });
    });

    return () => {
      subscription.unsubscribe();
    };
  }, [user, queryClient]);

  return query;
};

// Hook to update profile
export const useUpdateProfile = () => {
  const queryClient = useQueryClient();
  const { user } = useAuth();

  return useMutation({
    mutationFn: (updates: ProfileUpdate) => {
      if (!user) throw new Error('User not authenticated');
      return profileService.updateProfile(user.id, updates);
    },
    onSuccess: (updatedProfile) => {
      // Update the profile in cache
      queryClient.setQueryData(
        PROFILE_QUERY_KEYS.profile(updatedProfile.id),
        updatedProfile
      );

      // Also invalidate all profiles list if it exists
      queryClient.invalidateQueries({ 
        queryKey: PROFILE_QUERY_KEYS.allProfiles() 
      });
    },
  });
};

// Hook to update avatar
export const useUpdateAvatar = () => {
  const queryClient = useQueryClient();
  const { user } = useAuth();

  return useMutation({
    mutationFn: (file: File) => {
      if (!user) throw new Error('User not authenticated');
      return profileService.updateAvatar(user.id, file);
    },
    onSuccess: (updatedProfile) => {
      // Update the profile in cache
      queryClient.setQueryData(
        PROFILE_QUERY_KEYS.profile(updatedProfile.id),
        updatedProfile
      );
    },
  });
};

// Hook to get all profiles (admin only)
export const useAllProfiles = () => {
  return useQuery({
    queryKey: PROFILE_QUERY_KEYS.allProfiles(),
    queryFn: () => profileService.getAllProfiles(),
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
};
