import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { photoService, UserPhotoInsert } from '@/services/photoService';
import { useAuth } from '@/hooks/useAuth';
import { useEffect } from 'react';

export const PHOTO_QUERY_KEYS = {
  all: ['photos'] as const,
  featured: () => [...PHOTO_QUERY_KEYS.all, 'featured'] as const,
  userPhotos: (userId: string) => [...PHOTO_QUERY_KEYS.all, 'user', userId] as const,
  userLikes: (userId: string) => [...PHOTO_QUERY_KEYS.all, 'likes', userId] as const,
};

// Hook to get featured photos
export const useFeaturedPhotos = () => {
  const queryClient = useQueryClient();

  const query = useQuery({
    queryKey: PHOTO_QUERY_KEYS.featured(),
    queryFn: () => photoService.getFeaturedPhotos(),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Subscribe to real-time updates
  useEffect(() => {
    const photosSubscription = photoService.subscribeToPhotos(() => {
      queryClient.invalidateQueries({ queryKey: PHOTO_QUERY_KEYS.featured() });
    });

    const likesSubscription = photoService.subscribeToPhotoLikes(() => {
      queryClient.invalidateQueries({ queryKey: PHOTO_QUERY_KEYS.featured() });
    });

    return () => {
      photosSubscription.unsubscribe();
      likesSubscription.unsubscribe();
    };
  }, [queryClient]);

  return query;
};

// Hook to get user's photos
export const useUserPhotos = (userId?: string) => {
  const { user } = useAuth();
  const targetUserId = userId || user?.id;

  return useQuery({
    queryKey: PHOTO_QUERY_KEYS.userPhotos(targetUserId || ''),
    queryFn: () => photoService.getUserPhotos(targetUserId!),
    enabled: !!targetUserId,
  });
};

// Hook to upload and create a photo
export const useCreatePhoto = () => {
  const queryClient = useQueryClient();
  const { user } = useAuth();

  return useMutation({
    mutationFn: async ({ file, photoData }: { file: File; photoData: Omit<UserPhotoInsert, 'user_id' | 'image_url'> }) => {
      if (!user) throw new Error('User not authenticated');

      // Upload the file first
      const imageUrl = await photoService.uploadPhoto(file, user.id);

      // Create the photo record
      return photoService.createPhoto({
        ...photoData,
        user_id: user.id,
        image_url: imageUrl,
      });
    },
    onSuccess: () => {
      // Invalidate user photos and featured photos
      if (user) {
        queryClient.invalidateQueries({ queryKey: PHOTO_QUERY_KEYS.userPhotos(user.id) });
      }
      queryClient.invalidateQueries({ queryKey: PHOTO_QUERY_KEYS.featured() });
    },
  });
};

// Hook to delete a photo
export const useDeletePhoto = () => {
  const queryClient = useQueryClient();
  const { user } = useAuth();

  return useMutation({
    mutationFn: (photoId: string) => {
      if (!user) throw new Error('User not authenticated');
      return photoService.deletePhoto(photoId, user.id);
    },
    onSuccess: () => {
      // Invalidate user photos and featured photos
      if (user) {
        queryClient.invalidateQueries({ queryKey: PHOTO_QUERY_KEYS.userPhotos(user.id) });
      }
      queryClient.invalidateQueries({ queryKey: PHOTO_QUERY_KEYS.featured() });
    },
  });
};

// Hook to like a photo
export const useLikePhoto = () => {
  const queryClient = useQueryClient();
  const { user } = useAuth();

  return useMutation({
    mutationFn: (photoId: string) => {
      if (!user) throw new Error('User not authenticated');
      return photoService.likePhoto(photoId, user.id);
    },
    onSuccess: () => {
      // Invalidate featured photos to refresh likes
      queryClient.invalidateQueries({ queryKey: PHOTO_QUERY_KEYS.featured() });
    },
  });
};

// Hook to unlike a photo
export const useUnlikePhoto = () => {
  const queryClient = useQueryClient();
  const { user } = useAuth();

  return useMutation({
    mutationFn: (photoId: string) => {
      if (!user) throw new Error('User not authenticated');
      return photoService.unlikePhoto(photoId, user.id);
    },
    onSuccess: () => {
      // Invalidate featured photos to refresh likes
      queryClient.invalidateQueries({ queryKey: PHOTO_QUERY_KEYS.featured() });
    },
  });
};

// Hook to toggle like status
export const useTogglePhotoLike = () => {
  const { user } = useAuth();
  const likePhoto = useLikePhoto();
  const unlikePhoto = useUnlikePhoto();

  return useMutation({
    mutationFn: async ({ photoId, isLiked }: { photoId: string; isLiked: boolean }) => {
      if (!user) throw new Error('User not authenticated');

      if (isLiked) {
        return unlikePhoto.mutateAsync(photoId);
      } else {
        return likePhoto.mutateAsync(photoId);
      }
    },
  });
};

// Hook to check if user has liked a photo
export const useHasUserLikedPhoto = (photoId: string) => {
  const { user } = useAuth();

  return useQuery({
    queryKey: [...PHOTO_QUERY_KEYS.userLikes(user?.id || ''), photoId],
    queryFn: () => {
      if (!user) return false;
      return photoService.hasUserLikedPhoto(photoId, user.id);
    },
    enabled: !!user && !!photoId,
  });
};
