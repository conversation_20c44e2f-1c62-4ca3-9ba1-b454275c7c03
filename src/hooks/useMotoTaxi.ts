import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { motoTaxiService, DriverFilters, MotoTaxiDriver, MotoTaxiDriverUpdate } from '@/services/motoTaxiService';
import { useEffect } from 'react';

export const MOTO_TAXI_QUERY_KEYS = {
  all: ['moto-taxi'] as const,
  drivers: () => [...MOTO_TAXI_QUERY_KEYS.all, 'drivers'] as const,
  driversWithFilters: (filters: DriverFilters) => [...MOTO_TAXI_QUERY_KEYS.drivers(), filters] as const,
  driver: (id: string) => [...MOTO_TAXI_QUERY_KEYS.all, 'driver', id] as const,
};

// Hook to get all drivers with filters
export const useDrivers = (filters?: DriverFilters) => {
  return useQuery({
    queryKey: MOTO_TAXI_QUERY_KEYS.driversWithFilters(filters || {}),
    queryFn: () => motoTaxiService.getDrivers(filters),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Hook to get a single driver
export const useDriver = (id: string) => {
  return useQuery({
    queryKey: MOTO_TAXI_QUERY_KEYS.driver(id),
    queryFn: () => motoTaxiService.getDriver(id),
    enabled: !!id,
  });
};

// Hook to update driver availability
export const useUpdateDriverAvailability = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, isAvailable }: { id: string; isAvailable: boolean }) =>
      motoTaxiService.updateDriverAvailability(id, isAvailable),
    onSuccess: (updatedDriver) => {
      // Update the specific driver in cache
      queryClient.setQueryData(
        MOTO_TAXI_QUERY_KEYS.driver(updatedDriver.id),
        updatedDriver
      );

      // Invalidate drivers list to refresh
      queryClient.invalidateQueries({
        queryKey: MOTO_TAXI_QUERY_KEYS.drivers()
      });
    },
  });
};

// Hook to update driver
export const useUpdateDriver = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, updates }: { id: string; updates: MotoTaxiDriverUpdate }) =>
      motoTaxiService.updateDriver(id, updates),
    onSuccess: (updatedDriver) => {
      // Update the specific driver in cache
      queryClient.setQueryData(
        MOTO_TAXI_QUERY_KEYS.driver(updatedDriver.id),
        updatedDriver
      );

      // Invalidate drivers list to refresh
      queryClient.invalidateQueries({
        queryKey: MOTO_TAXI_QUERY_KEYS.drivers()
      });
    },
  });
};

// Hook for real-time driver updates
export const useDriversRealtime = (filters?: DriverFilters) => {
  const queryClient = useQueryClient();
  const queryKey = MOTO_TAXI_QUERY_KEYS.driversWithFilters(filters || {});

  useEffect(() => {
    const subscription = motoTaxiService.subscribeToDrivers((payload) => {
      // Invalidate and refetch drivers when changes occur
      queryClient.invalidateQueries({ queryKey });
    });

    return () => {
      subscription.unsubscribe();
    };
  }, [queryClient, queryKey]);

  return useDrivers(filters);
};

// Hook for drivers by vehicle type with real-time updates
export const useDriversByType = (vehicleType: 'motorcycle' | 'motorrete') => {
  const queryClient = useQueryClient();
  const filters = { vehicleType };
  const queryKey = MOTO_TAXI_QUERY_KEYS.driversWithFilters(filters);

  const query = useQuery({
    queryKey,
    queryFn: () => motoTaxiService.getDrivers(filters),
    staleTime: 5 * 60 * 1000,
  });

  useEffect(() => {
    const subscription = motoTaxiService.subscribeToDriversByType(
      vehicleType,
      (drivers: MotoTaxiDriver[]) => {
        queryClient.setQueryData(queryKey, drivers);
      }
    );

    return () => {
      subscription.unsubscribe();
    };
  }, [queryClient, queryKey, vehicleType]);

  return query;
};
