import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { beachLocationService, BeachLocationInsert, BeachLocationUpdate } from '@/services/beachLocationService';
import { useEffect } from 'react';

export const BEACH_LOCATION_QUERY_KEYS = {
  all: ['beach-locations'] as const,
  locations: () => [...BEACH_LOCATION_QUERY_KEYS.all, 'list'] as const,
  featured: () => [...BEACH_LOCATION_QUERY_KEYS.all, 'featured'] as const,
  location: (slug: string) => [...BEACH_LOCATION_QUERY_KEYS.all, 'location', slug] as const,
  locationById: (id: string) => [...BEACH_LOCATION_QUERY_KEYS.all, 'location-by-id', id] as const,
  search: (query: string) => [...BEACH_LOCATION_QUERY_KEYS.all, 'search', query] as const,
  nearby: (lat: number, lng: number, radius: number) => [...BEACH_LOCATION_QUERY_KEYS.all, 'nearby', lat, lng, radius] as const,
};

// Hook to get all beach locations
export const useBeachLocations = () => {
  return useQuery({
    queryKey: BEACH_LOCATION_QUERY_KEYS.locations(),
    queryFn: () => beachLocationService.getBeachLocations(),
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
};

// Hook to get featured beach locations
export const useFeaturedBeachLocations = () => {
  return useQuery({
    queryKey: BEACH_LOCATION_QUERY_KEYS.featured(),
    queryFn: () => beachLocationService.getFeaturedBeachLocations(),
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
};

// Hook to get a beach location by slug
export const useBeachLocationBySlug = (slug: string) => {
  return useQuery({
    queryKey: BEACH_LOCATION_QUERY_KEYS.location(slug),
    queryFn: () => beachLocationService.getBeachLocationBySlug(slug),
    enabled: !!slug,
  });
};

// Hook to get a beach location by ID
export const useBeachLocationById = (id: string) => {
  return useQuery({
    queryKey: BEACH_LOCATION_QUERY_KEYS.locationById(id),
    queryFn: () => beachLocationService.getBeachLocationById(id),
    enabled: !!id,
  });
};

// Hook to search beach locations
export const useSearchBeachLocations = (query: string) => {
  return useQuery({
    queryKey: BEACH_LOCATION_QUERY_KEYS.search(query),
    queryFn: () => beachLocationService.searchBeachLocations(query),
    enabled: query.length >= 2, // Only search if query is at least 2 characters
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Hook to get nearby beach locations
export const useNearbyBeachLocations = (latitude: number, longitude: number, radiusKm: number = 10) => {
  return useQuery({
    queryKey: BEACH_LOCATION_QUERY_KEYS.nearby(latitude, longitude, radiusKm),
    queryFn: () => beachLocationService.getNearbyBeachLocations(latitude, longitude, radiusKm),
    enabled: !!latitude && !!longitude,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
};

// Hook to create a beach location (admin only)
export const useCreateBeachLocation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (location: BeachLocationInsert) =>
      beachLocationService.createBeachLocation(location),
    onSuccess: () => {
      // Invalidate all beach location queries
      queryClient.invalidateQueries({
        queryKey: BEACH_LOCATION_QUERY_KEYS.all
      });
    },
  });
};

// Hook to update a beach location (admin only)
export const useUpdateBeachLocation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, updates }: { id: string; updates: BeachLocationUpdate }) =>
      beachLocationService.updateBeachLocation(id, updates),
    onSuccess: (updatedLocation) => {
      // Update the specific location in cache
      queryClient.setQueryData(
        BEACH_LOCATION_QUERY_KEYS.location(updatedLocation.slug),
        updatedLocation
      );
      queryClient.setQueryData(
        BEACH_LOCATION_QUERY_KEYS.locationById(updatedLocation.id),
        updatedLocation
      );

      // Invalidate list queries
      queryClient.invalidateQueries({
        queryKey: BEACH_LOCATION_QUERY_KEYS.locations()
      });
      queryClient.invalidateQueries({
        queryKey: BEACH_LOCATION_QUERY_KEYS.featured()
      });
    },
  });
};

// Hook to delete a beach location (admin only)
export const useDeleteBeachLocation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => beachLocationService.deleteBeachLocation(id),
    onSuccess: () => {
      // Invalidate all beach location queries
      queryClient.invalidateQueries({
        queryKey: BEACH_LOCATION_QUERY_KEYS.all
      });
    },
  });
};

// Hook for real-time beach location updates
export const useBeachLocationsRealtime = () => {
  const queryClient = useQueryClient();

  const query = useQuery({
    queryKey: BEACH_LOCATION_QUERY_KEYS.locations(),
    queryFn: () => beachLocationService.getBeachLocations(),
    staleTime: 10 * 60 * 1000,
  });

  useEffect(() => {
    const subscription = beachLocationService.subscribeToBeachLocations(() => {
      // Invalidate and refetch beach locations when changes occur
      queryClient.invalidateQueries({ 
        queryKey: BEACH_LOCATION_QUERY_KEYS.all 
      });
    });

    return () => {
      subscription.unsubscribe();
    };
  }, [queryClient]);

  return query;
};
