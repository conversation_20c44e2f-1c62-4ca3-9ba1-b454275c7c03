import { Waves, TrendingUp, TrendingDown } from 'lucide-react';
import { Link } from 'react-router-dom';

// Mock tide data - will be replaced with real API
const mockTideData = {
  nextTide: {
    type: 'high', // 'high' or 'low'
    time: '16:45',
    height: 2.8
  },
  timeUntil: '2h 15min'
};

export default function TideWidget() {
  const { nextTide, timeUntil } = mockTideData;
  const isHighTide = nextTide.type === 'high';

  return (
    <Link to="/weather" className="bg-white rounded-xl border border-border p-4 sm:p-6 hover:shadow-lg transition-all duration-200 block">
      <div className="flex items-start justify-between gap-2 sm:gap-3 mb-3 sm:mb-4">
        <div className="flex items-start gap-3 sm:gap-4 flex-1 min-w-0">
          <div className={`p-2 sm:p-3 rounded-xl ${isHighTide ? 'bg-gradient-to-br from-blue-500 to-blue-600' : 'bg-gradient-to-br from-slate-500 to-slate-600'} text-white flex-shrink-0`}>
            <Waves size={20} className="sm:w-6 sm:h-6" />
          </div>
          <div className="flex-1 min-w-0 overflow-hidden">
            <h3 className="font-semibold text-base sm:text-lg text-foreground mb-1 truncate">Próxima Maré</h3>
            <p className="text-sm sm:text-base text-muted-foreground mb-2 truncate">
              {isHighTide ? 'Alta' : 'Baixa'} • {nextTide.height}m
            </p>
            <div className="flex items-center gap-1 text-xs sm:text-sm text-blue-600">
              {isHighTide ? <TrendingUp size={12} className="sm:w-4 sm:h-4 flex-shrink-0" /> : <TrendingDown size={12} className="sm:w-4 sm:h-4 flex-shrink-0" />}
              <span className="truncate">em {timeUntil}</span>
            </div>
          </div>
        </div>

        <div className="text-right flex-shrink-0">
          <div className="text-xl sm:text-2xl md:text-3xl font-bold text-foreground">{nextTide.time}</div>
        </div>
      </div>

      <div className="flex items-center justify-between pt-3 sm:pt-4 border-t border-border">
        <span className="text-xs sm:text-sm text-muted-foreground truncate flex-1 mr-2">
          {isHighTide ? 'Ideal para banho' : 'Melhor para caminhada'}
        </span>
        <div className="flex items-center gap-1 flex-shrink-0">
          {isHighTide ? (
            <TrendingUp size={12} className="sm:w-4 sm:h-4 text-blue-500" />
          ) : (
            <TrendingDown size={12} className="sm:w-4 sm:h-4 text-slate-500" />
          )}
        </div>
      </div>
    </Link>
  );
}