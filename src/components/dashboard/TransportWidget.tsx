import { Ship, Clock, ChevronRight } from 'lucide-react';
import { Link } from 'react-router-dom';
import { useState, useEffect } from 'react';

// Mock schedules - will be replaced with Supabase data
const mockSchedules = [
  { time: '15:00', type: 'Municipal', duration: 40, price: 4.60 },
  { time: '16:00', type: 'Geladão', duration: 20, price: 4.60 },
  { time: '18:30', type: 'Municipal', duration: 40, price: 9.20 }
];

export default function TransportWidget() {
  const [nextBoat, setNextBoat] = useState<typeof mockSchedules[0] | null>(null);
  const [countdown, setCountdown] = useState('');

  useEffect(() => {
    const updateNextBoat = () => {
      const now = new Date();
      const currentTime = now.getHours() * 60 + now.getMinutes();
      
      const next = mockSchedules.find(schedule => {
        const [hours, minutes] = schedule.time.split(':').map(Number);
        const scheduleTime = hours * 60 + minutes;
        return scheduleTime > currentTime;
      });
      
      setNextBoat(next || mockSchedules[0]); // Fallback to first boat of next day
      
      if (next) {
        const [hours, minutes] = next.time.split(':').map(Number);
        const scheduleTime = new Date();
        scheduleTime.setHours(hours, minutes, 0, 0);
        
        const diff = scheduleTime.getTime() - now.getTime();
        const hoursLeft = Math.floor(diff / (1000 * 60 * 60));
        const minutesLeft = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
        
        if (diff > 0) {
          setCountdown(`${hoursLeft}h ${minutesLeft}min`);
        } else {
          setCountdown('Próximo horário');
        }
      }
    };

    updateNextBoat();
    const interval = setInterval(updateNextBoat, 60000); // Update every minute
    
    return () => clearInterval(interval);
  }, []);

  if (!nextBoat) return null;

  return (
    <Link to="/transport" className="bg-white rounded-xl border border-border p-4 sm:p-6 hover:shadow-lg transition-all duration-200 block">
      <div className="flex items-start justify-between gap-2 sm:gap-3">
        <div className="flex items-start gap-3 sm:gap-4 flex-1 min-w-0">
          <div className="p-2 sm:p-3 rounded-xl bg-gradient-to-br from-emerald-600 to-emerald-700 text-white flex-shrink-0">
            <Ship size={20} className="sm:w-6 sm:h-6" />
          </div>
          <div className="flex-1 min-w-0 overflow-hidden">
            <h3 className="font-semibold text-base sm:text-lg text-foreground mb-1 truncate">Próximo Barco</h3>
            <p className="text-sm sm:text-base text-muted-foreground mb-2 truncate">{nextBoat.type} • {nextBoat.duration}min</p>
            <div className="flex items-center gap-1 text-xs sm:text-sm text-emerald-600">
              <Clock size={12} className="sm:w-4 sm:h-4 flex-shrink-0" />
              <span className="truncate">{countdown}</span>
            </div>
          </div>
        </div>

        <div className="text-right flex-shrink-0">
          <div className="text-xl sm:text-2xl md:text-3xl font-bold text-foreground">{nextBoat.time}</div>
        </div>
      </div>
      
      <div className="flex items-center justify-between pt-4 mt-4 border-t border-border">
        <span className="text-xs text-muted-foreground">De Icoaraci para Cotijuba</span>
        <div className="flex items-center gap-1 text-xs text-muted-foreground">
          <span>R$ {nextBoat.price.toFixed(2)}</span>
          <ChevronRight size={14} />
        </div>
      </div>
    </Link>
  );
}