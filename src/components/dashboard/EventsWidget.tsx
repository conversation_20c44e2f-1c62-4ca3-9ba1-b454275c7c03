import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { CalendarDays } from 'lucide-react';

export default function EventsWidget() {
  return (
    <div className="bg-white rounded-xl border border-border p-4 sm:p-6 hover:shadow-lg transition-all duration-200 block">
      <div className="flex items-start justify-between gap-2 sm:gap-3">
        <div className="flex items-start gap-3 sm:gap-4 flex-1 min-w-0">
          <div className="p-2 sm:p-3 rounded-xl bg-gradient-to-br from-purple-500 to-purple-600 text-white flex-shrink-0">
            <CalendarDays size={20} className="sm:w-6 sm:h-6" />
          </div>
          <div className="flex-1 min-w-0 overflow-hidden">
            <h3 className="font-semibold text-base sm:text-lg text-foreground mb-1 truncate">Eventos</h3>
            <p className="text-sm sm:text-base text-muted-foreground line-clamp-2">2 eventos nas próximas semanas</p>
          </div>
        </div>
      </div>
    </div>
  );
}
