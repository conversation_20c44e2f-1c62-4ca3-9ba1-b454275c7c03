import { CloudSun, Thermometer, Eye } from 'lucide-react';
import { Link } from 'react-router-dom';

// Mock data - will be replaced with real API
const mockWeatherData = {
  temperature: 28,
  condition: 'Parcialmente nublado',
  feelsLike: 32,
  icon: CloudSun,
  humidity: 75,
  windSpeed: 15
};

export default function WeatherWidget() {
  const { temperature, condition, feelsLike, icon: WeatherIcon } = mockWeatherData;

  return (
    <Link to="/weather" className="bg-white rounded-xl border border-border p-4 sm:p-6 hover:shadow-lg transition-all duration-200 block">
      <div className="flex items-start justify-between gap-2 sm:gap-3">
        <div className="flex items-start gap-3 sm:gap-4 flex-1 min-w-0">
          <div className="p-2 sm:p-3 rounded-xl bg-gradient-to-br from-blue-500 to-blue-600 text-white flex-shrink-0">
            <WeatherIcon size={20} className="sm:w-6 sm:h-6" />
          </div>
          <div className="flex-1 min-w-0 overflow-hidden">
            <h3 className="font-semibold text-base sm:text-lg text-foreground mb-1 truncate">Clima Agora</h3>
            <p className="text-sm sm:text-base text-muted-foreground leading-relaxed break-words line-clamp-2">{condition}</p>
            <div className="flex items-center gap-1 text-xs sm:text-sm text-muted-foreground mt-2">
              <Thermometer size={12} className="sm:w-4 sm:h-4 flex-shrink-0" />
              <span className="truncate">Sensação {feelsLike}°C</span>
            </div>
          </div>
        </div>

        <div className="text-right flex-shrink-0">
          <div className="text-xl sm:text-2xl md:text-3xl font-bold text-foreground">{temperature}°C</div>
        </div>
      </div>
      
      <div className="flex items-center justify-between pt-4 mt-4 border-t border-border">
        <span className="text-xs text-muted-foreground">Ver previsão completa</span>
        <Eye size={14} className="text-muted-foreground" />
      </div>
    </Link>
  );
}