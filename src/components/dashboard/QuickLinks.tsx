import { MapPin, Waves, Hotel, UtensilsCrossed, Bike, Phone } from 'lucide-react';
import { Link } from 'react-router-dom';

const quickLinks = [
  { icon: MapPin, label: 'Mapa', path: '/mapa', color: 'text-blue-600' },
  { icon: Waves, label: 'Praias', path: '/locations', color: 'text-cyan-600' },
  { icon: Hotel, label: 'Hospedagem', path: '/accommodations', color: 'text-purple-600' },
  { icon: UtensilsCrossed, label: 'Restaurantes', path: '/restaurants', color: 'text-orange-600' },
  { icon: Bike, label: 'Moto-Táxi', path: '/moto-taxi', color: 'text-green-600' },
  { icon: Phone, label: 'Emergência', path: '/emergency', color: 'text-red-600' }
];

export default function QuickLinks() {
  return (
    <div className="space-y-4 sm:space-y-6">
      <h2 className="text-lg sm:text-xl md:text-2xl font-semibold text-foreground"><PERSON><PERSON></h2>

      <div className="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-6 lg:grid-cols-6 gap-3 sm:gap-4 md:gap-6">
        {quickLinks.map(({ icon: Icon, label, path, color }) => (
          <Link
            key={path}
            to={path}
            className="quick-link group"
          >
            <div className={`p-3 sm:p-4 rounded-xl bg-slate-50 group-hover:bg-slate-100 transition-colors ${color}`}>
              <Icon size={24} className="sm:w-6 sm:h-6 md:w-7 md:h-7" />
            </div>
            <span className="text-xs sm:text-sm md:text-base font-medium text-foreground mt-2 text-center leading-tight line-clamp-2">{label}</span>
          </Link>
        ))}
      </div>
    </div>
  );
}