import React, { useEffect, useMemo, useRef, useState } from 'react';
import { Heart, Dot, Camera } from 'lucide-react';
import { Carousel, CarouselContent, CarouselItem } from '@/components/ui/carousel';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { usePhotoStore } from '@/stores/usePhotoStore';
import { cn } from '@/lib/utils';

// Carrossel de fotos - topo da Home
export const PhotoCarousel: React.FC = () => {
  const { featuredPhotos, toggleLike } = usePhotoStore();
  const [selected, setSelected] = useState(0);
  const [isPaused, setIsPaused] = useState(false);
  const apiRef = useRef<any>(null);

  // Atualiza seleção para os dots
  const setApi = (api: any) => {
    apiRef.current = api;
    const onSelect = () => setSelected(api.selectedScrollSnap());
    api.on('select', onSelect);
    onSelect();
  };

  // Autoplay a cada 5s (pausa on hover/touch)
  useEffect(() => {
    if (!apiRef.current || isPaused) return;
    const id = setInterval(() => apiRef.current?.scrollNext(), 5000);
    return () => clearInterval(id);
  }, [isPaused]);

  return (
    <section aria-label="Galeria de fotos da comunidade" className="mb-4 sm:mb-6">
      <Carousel setApi={setApi} className="w-full">
        <CarouselContent>
          {featuredPhotos.map((p, i) => (
            <CarouselItem key={p.id}>
              <Card className="relative overflow-hidden rounded-xl h-[280px] sm:h-[320px] md:h-[400px] lg:h-[480px] xl:h-[520px]">
                <img
                  src={`${p.image_url}`}
                  alt={`${p.caption} por ${p.user_name}`}
                  className="absolute inset-0 w-full h-full object-cover transition-opacity duration-500"
                  loading="lazy"
                  onLoad={(e) => (e.currentTarget.style.opacity = '1')}
                  onError={(e) => (e.currentTarget.style.opacity = '1')}
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/10 to-transparent" />
                <div className="absolute bottom-3 left-3 right-3 sm:bottom-4 sm:left-4 sm:right-4 md:bottom-6 md:left-6 md:right-6 flex items-end justify-between gap-3">
                  <div className="text-white">
                    <p className="text-sm sm:text-base md:text-lg leading-5 opacity-90">{p.caption}</p>
                    <p className="text-xs sm:text-sm leading-4 opacity-80">Por {p.user_name}</p>
                  </div>
                  <Button
                    aria-label={p.is_liked ? 'Descurtir' : 'Curtir'}
                    onClick={() => toggleLike(p.id)}
                    size="sm"
                    variant="secondary"
                    className={cn('min-w-[44px] min-h-[44px] sm:min-w-[48px] sm:min-h-[48px] rounded-full bg-white/20 text-white hover:bg-white/30 backdrop-blur')}
                  >
                    <Heart className={cn('mr-1 sm:mr-2', p.is_liked ? 'fill-current' : '')} size={16} />
                    <span className="text-white font-medium text-sm sm:text-base">{p.likes_count}</span>
                  </Button>
                </div>
              </Card>
            </CarouselItem>
          ))}
        </CarouselContent>
      </Carousel>

      {/* Dots indicadores */}
      <div
        className="mt-3 sm:mt-4 flex items-center justify-center gap-2 sm:gap-3"
        onMouseEnter={() => setIsPaused(true)}
        onMouseLeave={() => setIsPaused(false)}
        onTouchStart={() => setIsPaused(true)}
        onTouchEnd={() => setIsPaused(false)}
      >
        {featuredPhotos.map((_, i) => (
          <button
            key={i}
            aria-label={`Ir para foto ${i + 1}`}
            className={cn(
              'p-2 rounded-full transition-colors', // Added padding for touch area
              'flex items-center justify-center', // Center the dot
              i === selected ? 'bg-primary/20' : 'bg-muted/20' // Background for touch area
            )}
            onClick={() => apiRef.current?.scrollTo(i)}
          >
            <div className={cn(
              'h-2 w-2 sm:h-3 sm:w-3 rounded-full transition-colors',
              i === selected ? 'bg-primary' : 'bg-muted'
            )} />
          </button>
        ))}
      </div>
    </section>
  );
};

export default PhotoCarousel;
