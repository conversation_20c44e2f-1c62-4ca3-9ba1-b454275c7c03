import React, { useEffect, useRef, useState } from 'react';
import { Heart, Camera, Loader2 } from 'lucide-react';
import { Carousel, CarouselContent, CarouselItem } from '@/components/ui/carousel';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { useFeaturedPhotos, useTogglePhotoLike } from '@/hooks/usePhotos';
import { useAuth } from '@/hooks/useAuth';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';

// Carrossel de fotos - topo da Home
export const PhotoCarousel: React.FC = () => {
  const { user } = useAuth();
  const { data: featuredPhotos = [], isLoading, error } = useFeaturedPhotos();
  const toggleLike = useTogglePhotoLike();
  const [selected, setSelected] = useState(0);
  const [isPaused, setIsPaused] = useState(false);
  const apiRef = useRef<any>(null);

  const handleToggleLike = async (photoId: string, isCurrentlyLiked: boolean) => {
    if (!user) {
      toast.error('Faça login para curtir fotos');
      return;
    }

    try {
      await toggleLike.mutateAsync({ photoId, isLiked: isCurrentlyLiked });
    } catch (error) {
      toast.error('Erro ao curtir foto');
    }
  };

  // Atualiza seleção para os dots
  const setApi = (api: any) => {
    apiRef.current = api;
    const onSelect = () => setSelected(api.selectedScrollSnap());
    api.on('select', onSelect);
    onSelect();
  };

  // Autoplay a cada 5s (pausa on hover/touch)
  useEffect(() => {
    if (!apiRef.current || isPaused || featuredPhotos.length === 0) return;
    const id = setInterval(() => apiRef.current?.scrollNext(), 5000);
    return () => clearInterval(id);
  }, [isPaused, featuredPhotos.length]);

  if (isLoading) {
    return (
      <section aria-label="Galeria de fotos da comunidade" className="mb-4 sm:mb-6">
        <Card className="relative overflow-hidden rounded-xl h-[280px] sm:h-[320px] md:h-[400px] lg:h-[480px] xl:h-[520px]">
          <Skeleton className="w-full h-full" />
          <div className="absolute bottom-4 left-4 right-4 flex items-end justify-between gap-3">
            <div className="space-y-2">
              <Skeleton className="h-4 w-48" />
              <Skeleton className="h-3 w-32" />
            </div>
            <Skeleton className="h-12 w-20 rounded-full" />
          </div>
        </Card>
        <div className="mt-4 flex items-center justify-center gap-3">
          {[...Array(3)].map((_, i) => (
            <Skeleton key={i} className="h-3 w-3 rounded-full" />
          ))}
        </div>
      </section>
    );
  }

  if (error) {
    return (
      <section aria-label="Galeria de fotos da comunidade" className="mb-4 sm:mb-6">
        <Card className="relative overflow-hidden rounded-xl h-[280px] sm:h-[320px] md:h-[400px] lg:h-[480px] xl:h-[520px] flex items-center justify-center">
          <div className="text-center text-muted-foreground">
            <Camera className="h-12 w-12 mx-auto mb-2 opacity-50" />
            <p>Erro ao carregar fotos</p>
          </div>
        </Card>
      </section>
    );
  }

  if (featuredPhotos.length === 0) {
    return (
      <section aria-label="Galeria de fotos da comunidade" className="mb-4 sm:mb-6">
        <Card className="relative overflow-hidden rounded-xl h-[280px] sm:h-[320px] md:h-[400px] lg:h-[480px] xl:h-[520px] flex items-center justify-center">
          <div className="text-center text-muted-foreground">
            <Camera className="h-12 w-12 mx-auto mb-2 opacity-50" />
            <p>Nenhuma foto disponível</p>
            <p className="text-sm">Seja o primeiro a compartilhar uma foto!</p>
          </div>
        </Card>
      </section>
    );
  }

  return (
    <section aria-label="Galeria de fotos da comunidade" className="mb-4 sm:mb-6">
      <Carousel setApi={setApi} className="w-full">
        <CarouselContent>
          {featuredPhotos.map((photo) => {
            // Check if current user has liked this photo
            const isLikedByUser = photo.photo_likes?.some(like => like.user_id === user?.id) || false;

            return (
              <CarouselItem key={photo.id}>
                <Card className="relative overflow-hidden rounded-xl h-[280px] sm:h-[320px] md:h-[400px] lg:h-[480px] xl:h-[520px]">
                  <img
                    src={photo.image_url}
                    alt={`${photo.caption} por ${photo.user_profile?.display_name || 'Usuário'}`}
                    className="absolute inset-0 w-full h-full object-cover transition-opacity duration-500"
                    loading="lazy"
                    onLoad={(e) => (e.currentTarget.style.opacity = '1')}
                    onError={(e) => (e.currentTarget.style.opacity = '1')}
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/10 to-transparent" />
                  <div className="absolute bottom-3 left-3 right-3 sm:bottom-4 sm:left-4 sm:right-4 md:bottom-6 md:left-6 md:right-6 flex items-end justify-between gap-3">
                    <div className="text-white">
                      <p className="text-sm sm:text-base md:text-lg leading-5 opacity-90">
                        {photo.caption || 'Sem descrição'}
                      </p>
                      <p className="text-xs sm:text-sm leading-4 opacity-80">
                        Por {photo.user_profile?.display_name || 'Usuário'}
                      </p>
                    </div>
                    <Button
                      aria-label={isLikedByUser ? 'Descurtir' : 'Curtir'}
                      onClick={() => handleToggleLike(photo.id, isLikedByUser)}
                      size="sm"
                      variant="secondary"
                      disabled={toggleLike.isPending}
                      className={cn(
                        'min-w-[44px] min-h-[44px] sm:min-w-[48px] sm:min-h-[48px] rounded-full bg-white/20 text-white hover:bg-white/30 backdrop-blur',
                        toggleLike.isPending && 'opacity-50'
                      )}
                    >
                      {toggleLike.isPending ? (
                        <Loader2 className="mr-1 sm:mr-2 animate-spin" size={16} />
                      ) : (
                        <Heart className={cn('mr-1 sm:mr-2', isLikedByUser ? 'fill-current' : '')} size={16} />
                      )}
                      <span className="text-white font-medium text-sm sm:text-base">
                        {photo.likes_count || 0}
                      </span>
                    </Button>
                  </div>
                </Card>
              </CarouselItem>
            );
          })}
        </CarouselContent>
      </Carousel>

      {/* Dots indicadores */}
      <div
        className="mt-3 sm:mt-4 flex items-center justify-center gap-2 sm:gap-3"
        onMouseEnter={() => setIsPaused(true)}
        onMouseLeave={() => setIsPaused(false)}
        onTouchStart={() => setIsPaused(true)}
        onTouchEnd={() => setIsPaused(false)}
      >
        {featuredPhotos.map((_, i) => (
          <button
            key={i}
            aria-label={`Ir para foto ${i + 1}`}
            className={cn(
              'p-2 rounded-full transition-colors', // Added padding for touch area
              'flex items-center justify-center', // Center the dot
              i === selected ? 'bg-primary/20' : 'bg-muted/20' // Background for touch area
            )}
            onClick={() => apiRef.current?.scrollTo(i)}
          >
            <div className={cn(
              'h-2 w-2 sm:h-3 sm:w-3 rounded-full transition-colors',
              i === selected ? 'bg-primary' : 'bg-muted'
            )} />
          </button>
        ))}
      </div>
    </section>
  );
};

export default PhotoCarousel;
