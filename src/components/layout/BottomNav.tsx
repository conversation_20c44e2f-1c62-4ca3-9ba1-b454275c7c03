import { Info, MapPin, Bike, User } from 'lucide-react';
import { useLocation, Link } from 'react-router-dom';
import { cn } from '@/lib/utils';

const navItems = [
  { icon: Info, label: 'Informações', path: '/' },
  { icon: MapPin, label: 'Mapa', path: '/mapa' },
  { icon: Bike, label: 'Moto-Táxi', path: '/moto-taxi' },
  { icon: User, label: 'Perfil', path: '/perfil' }
];

export default function BottomNav() {
  const location = useLocation();

  return (
    <nav className="nav-bottom">
      <div className="flex items-center justify-around px-2 py-2">
        {navItems.map(({ icon: Icon, label, path }) => {
          const isActive =
            path === '/perfil'
              ? location.pathname.startsWith('/perfil')
              : location.pathname === path;
          
          return (
            <Link
              key={path}
              to={path}
              aria-label={label}
              className={cn(
                "flex flex-col items-center justify-center py-2 px-3 rounded-lg touch-target transition-all duration-200",
                isActive 
                  ? "text-primary bg-primary/10" 
                  : "text-muted-foreground hover:text-primary hover:bg-muted/50"
              )}
            >
              <Icon size={20} className="mb-1" />
              <span className="text-xs font-medium">{label}</span>
            </Link>
          );
        })}
      </div>
    </nav>
  );
}