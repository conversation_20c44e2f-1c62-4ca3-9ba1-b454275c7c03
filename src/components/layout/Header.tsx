import { MapPin, Wifi, WifiOff } from 'lucide-react';
import { useState, useEffect } from 'react';

interface HeaderProps {
  title: string;
  showLocation?: boolean;
  location?: string;
}

export default function Header({ title, showLocation = true, location = "Cotijuba" }: HeaderProps) {
  const [isOnline, setIsOnline] = useState(navigator.onLine);

  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  return (
    <header className="bg-white/95 backdrop-blur-md border-b border-slate-200 sticky top-0 z-40 px-4 py-3">
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <h1 className="text-xl font-bold text-foreground">{title}</h1>
          {showLocation && (
            <div className="flex items-center gap-1 mt-1">
              <MapPin size={14} className="text-muted-foreground" />
              <span className="text-sm text-muted-foreground">{location}</span>
            </div>
          )}
        </div>
        
        <div className="flex items-center gap-2">
          {isOnline ? (
            <div className="flex items-center gap-1 text-emerald-600">
              <Wifi size={16} />
              <span className="text-xs font-medium">Online</span>
            </div>
          ) : (
            <div className="flex items-center gap-1 text-amber-600">
              <WifiOff size={16} />
              <span className="text-xs font-medium">Offline</span>
            </div>
          )}
        </div>
      </div>
    </header>
  );
}