import { ReactNode } from 'react';
import { cn } from '@/lib/utils';

interface ContainerProps {
  children: ReactNode;
  className?: string;
  withBottomNav?: boolean;
}

export default function Container({ children, className, withBottomNav = true }: ContainerProps) {
  return (
    <div className={cn(
      "container mx-auto px-4 py-6",
      // Responsive max-widths: mobile-first, then tablet, then desktop
      "max-w-md sm:max-w-2xl md:max-w-4xl lg:max-w-6xl xl:max-w-7xl",
      // Responsive padding
      "sm:px-6 md:px-8 lg:px-12",
      withBottomNav && "pb-20", // Extra padding for bottom navigation
      className
    )}>
      {children}
    </div>
  );
}