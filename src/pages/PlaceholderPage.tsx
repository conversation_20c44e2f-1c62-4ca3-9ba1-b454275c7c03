import { Construction, ArrowLeft } from 'lucide-react';
import { Link } from 'react-router-dom';
import Header from '@/components/layout/Header';
import Container from '@/components/layout/Container';
import BottomNav from '@/components/layout/BottomNav';
import { Button } from '@/components/ui/button';

interface PlaceholderPageProps {
  title: string;
  description?: string;
  comingSoonFeatures?: string[];
}

export default function PlaceholderPage({ 
  title, 
  description = "Esta funcionalidade está sendo desenvolvida e estará disponível em breve!",
  comingSoonFeatures = []
}: PlaceholderPageProps) {
  return (
    <div className="min-h-screen bg-background">
      <Header title={title} />
      
      <Container>
        <div className="text-center py-12">
          <div className="flex justify-center mb-6">
            <div className="p-6 rounded-full bg-gradient-to-br from-orange-100 to-orange-200">
              <Construction size={48} className="text-orange-600" />
            </div>
          </div>
          
          <h2 className="text-2xl font-bold text-foreground mb-4">Em Breve</h2>
          <p className="text-muted-foreground mb-8 max-w-sm mx-auto">
            {description}
          </p>

          {comingSoonFeatures.length > 0 && (
            <div className="widget-card text-left mb-8">
              <h3 className="font-semibold text-foreground mb-3">Funcionalidades planejadas:</h3>
              <ul className="space-y-2">
                {comingSoonFeatures.map((feature, index) => (
                  <li key={index} className="flex items-start gap-2">
                    <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                    <span className="text-sm text-muted-foreground">{feature}</span>
                  </li>
                ))}
              </ul>
            </div>
          )}

          <Link to="/">
            <Button className="btn-ocean">
              <ArrowLeft size={16} className="mr-2" />
              Voltar ao Início
            </Button>
          </Link>
        </div>
      </Container>

      <BottomNav />
    </div>
  );
}