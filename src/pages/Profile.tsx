import React, { useState } from 'react';
import Header from '@/components/layout/Header';
import Container from '@/components/layout/Container';
import BottomNav from '@/components/layout/BottomNav';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Skeleton } from '@/components/ui/skeleton';
import { useAuth } from '@/hooks/useAuth';
import { useUserStore } from '@/stores/useUserStore';
import { useUserPhotos, useCreatePhoto, useDeletePhoto } from '@/hooks/usePhotos';
import { useCurrentUserProfile, useUpdateProfile, useUpdateAvatar } from '@/hooks/useProfile';
import { Trash2, Camera, LogIn, UserPlus, Settings, Heart, Star, MapPin, Edit } from 'lucide-react';
import { toast } from 'sonner';

export default function ProfilePage() {
  const { user, loading: authLoading, signOut } = useAuth();
  const { favorites, toggleFavorite } = useUserStore();
  const { data: profile, isLoading: profileLoading } = useCurrentUserProfile();
  const { data: userPhotos = [], isLoading: photosLoading } = useUserPhotos();
  const updateProfile = useUpdateProfile();
  const updateAvatar = useUpdateAvatar();
  const createPhoto = useCreatePhoto();
  const deletePhoto = useDeletePhoto();

  const [photoDialogOpen, setPhotoDialogOpen] = useState(false);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [file, setFile] = useState<File | null>(null);
  const [caption, setCaption] = useState('');
  const [displayName, setDisplayName] = useState(profile?.display_name || '');
  const [avatarFile, setAvatarFile] = useState<File | null>(null);

  const canSubmitPhoto = file && caption.trim().length >= 10 && caption.trim().length <= 200;
  const isLoading = authLoading || profileLoading;

  const handleSignOut = async () => {
    try {
      await signOut();
      toast.success('Logout realizado com sucesso');
    } catch (error) {
      toast.error('Erro ao fazer logout');
    }
  };

  const submitPhoto = async () => {
    if (!file || !user) return;

    try {
      await createPhoto.mutateAsync({
        file,
        photoData: {
          caption: caption.trim(),
          location_name: null,
          tags: null,
        }
      });

      setPhotoDialogOpen(false);
      setFile(null);
      setCaption('');
      toast.success('Foto enviada com sucesso!');
    } catch (error) {
      toast.error('Erro ao enviar foto');
    }
  };

  const handleDeletePhoto = async (photoId: string) => {
    try {
      await deletePhoto.mutateAsync(photoId);
      toast.success('Foto excluída com sucesso');
    } catch (error) {
      toast.error('Erro ao excluir foto');
    }
  };

  const handleUpdateProfile = async () => {
    try {
      if (avatarFile) {
        await updateAvatar.mutateAsync(avatarFile);
      }

      if (displayName !== profile?.display_name) {
        await updateProfile.mutateAsync({ display_name: displayName });
      }

      setEditDialogOpen(false);
      setAvatarFile(null);
      toast.success('Perfil atualizado com sucesso!');
    } catch (error) {
      toast.error('Erro ao atualizar perfil');
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-background">
        <Header title="Perfil" />
        <Container>
          <Card className="p-4 mb-4">
            <div className="flex items-center gap-3">
              <Skeleton className="h-14 w-14 rounded-full" />
              <div className="flex-1 space-y-2">
                <Skeleton className="h-5 w-32" />
                <Skeleton className="h-4 w-48" />
              </div>
              <Skeleton className="h-9 w-20" />
            </div>
          </Card>
        </Container>
        <BottomNav />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <Header title="Perfil" />
      <Container>
        {/* Header */}
        <Card className="p-4 mb-4">
          <div className="flex items-center gap-3">
            <Avatar className="h-14 w-14">
              <AvatarImage src={profile?.avatar_url || undefined} />
              <AvatarFallback>
                {profile?.display_name?.slice(0, 2).toUpperCase() || user?.email?.slice(0, 2).toUpperCase() || 'U'}
              </AvatarFallback>
            </Avatar>
            <div className="flex-1">
              <h2 className="font-semibold text-foreground">
                {profile?.display_name || user?.email || 'Usuário'}
              </h2>
              <p className="text-sm text-muted-foreground">
                {user?.email || 'Usuário logado'}
              </p>
            </div>
            {user ? (
              <div className="flex gap-2">
                <Dialog open={editDialogOpen} onOpenChange={setEditDialogOpen}>
                  <DialogTrigger asChild>
                    <Button variant="outline" size="sm">
                      <Edit className="h-4 w-4" />
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>Editar Perfil</DialogTitle>
                    </DialogHeader>
                    <div className="space-y-4">
                      <div>
                        <Label htmlFor="displayName">Nome de exibição</Label>
                        <Input
                          id="displayName"
                          value={displayName}
                          onChange={(e) => setDisplayName(e.target.value)}
                          placeholder="Seu nome"
                        />
                      </div>
                      <div>
                        <Label htmlFor="avatar">Foto do perfil</Label>
                        <Input
                          id="avatar"
                          type="file"
                          accept="image/*"
                          onChange={(e) => setAvatarFile(e.target.files?.[0] || null)}
                        />
                      </div>
                      <div className="flex justify-end gap-2">
                        <Button variant="outline" onClick={() => setEditDialogOpen(false)}>
                          Cancelar
                        </Button>
                        <Button
                          onClick={handleUpdateProfile}
                          disabled={updateProfile.isPending || updateAvatar.isPending}
                        >
                          Salvar
                        </Button>
                      </div>
                    </div>
                  </DialogContent>
                </Dialog>
                <Button variant="secondary" onClick={handleSignOut}>
                  <Settings className="h-4 w-4" />
                </Button>
              </div>
            ) : (
              <div className="flex gap-2">
                <Button onClick={() => window.location.href = '/login'}>
                  <LogIn className="h-4 w-4" /> Entrar
                </Button>
              </div>
            )}
          </div>
          {user && (
            <div className="mt-3 text-sm text-muted-foreground">
              Membro desde: {profile?.member_since ? new Date(profile.member_since).toLocaleDateString('pt-BR') : '—'} •
              📸 {userPhotos.length} fotos •
              ⭐ 0 avaliações •
              ❤️ {favorites.length} favoritos
            </div>
          )}
        </Card>

        {/* Ações */}
        <div className="grid grid-cols-2 md:grid-cols-3 gap-3 mb-6">
          {user && (
            <Dialog open={photoDialogOpen} onOpenChange={setPhotoDialogOpen}>
              <DialogTrigger asChild>
                <Card role="button" tabIndex={0} className="p-4 text-center hover:bg-accent cursor-pointer">
                  <div className="flex flex-col items-center">
                    <Camera className="h-6 w-6" />
                    <span className="mt-2 font-medium">Enviar Foto</span>
                  </div>
                </Card>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Enviar Foto</DialogTitle>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="photo">Selecione uma foto</Label>
                    <Input
                      id="photo"
                      type="file"
                      accept="image/jpeg,image/png,image/webp"
                      onChange={(e) => setFile(e.target.files?.[0] || null)}
                    />
                  </div>
                  {file && (
                    <img
                      src={URL.createObjectURL(file)}
                      alt="Pré-visualização"
                      className="max-w-full max-h-[300px] object-contain rounded mx-auto"
                    />
                  )}
                  <div>
                    <Label htmlFor="caption">Descrição (obrigatório)</Label>
                    <textarea
                      id="caption"
                      className="w-full border rounded p-2 text-sm min-h-[80px] resize-none"
                      placeholder="Descreva sua foto..."
                      value={caption}
                      onChange={(e) => setCaption(e.target.value)}
                      maxLength={200}
                    />
                    <div className="text-xs text-muted-foreground text-right mt-1">
                      {caption.length}/200 caracteres
                    </div>
                  </div>
                  <div className="flex justify-end gap-2">
                    <Button variant="outline" onClick={() => setPhotoDialogOpen(false)}>
                      Cancelar
                    </Button>
                    <Button
                      disabled={!canSubmitPhoto || createPhoto.isPending}
                      onClick={submitPhoto}
                    >
                      {createPhoto.isPending ? 'Enviando...' : 'Enviar Foto'}
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          )}

          <Card className="p-4 text-center cursor-pointer hover:bg-accent">
            <div className="flex flex-col items-center">
              <Star className="h-6 w-6" />
              <span className="mt-2 block font-medium">Minhas Avaliações</span>
            </div>
          </Card>
          <Card className="p-4 text-center cursor-pointer hover:bg-accent">
            <div className="flex flex-col items-center">
              <Heart className="h-6 w-6" />
              <span className="mt-2 block font-medium">Favoritos ({favorites.length})</span>
            </div>
          </Card>
          <Card className="p-4 text-center cursor-pointer hover:bg-accent">
            <div className="flex flex-col items-center">
              <MapPin className="h-6 w-6" />
              <span className="mt-2 block font-medium">Sugerir Local</span>
            </div>
          </Card>
          <Card className="p-4 text-center cursor-pointer hover:bg-accent">
            <div className="flex flex-col items-center">
              <Settings className="h-6 w-6" />
              <span className="mt-2 block font-medium">Configurações</span>
            </div>
          </Card>
        </div>

        {/* Minhas fotos */}
        {user && (
          <section>
            <h3 className="text-lg font-semibold mb-3">Minhas Fotos</h3>
            {photosLoading ? (
              <div className="grid grid-cols-3 gap-2">
                {[...Array(6)].map((_, i) => (
                  <Skeleton key={i} className="w-full h-24 rounded" />
                ))}
              </div>
            ) : userPhotos.length > 0 ? (
              <div className="grid grid-cols-3 gap-2">
                {userPhotos.map((photo) => (
                  <div key={photo.id} className="relative group">
                    <img
                      src={photo.image_url}
                      alt={photo.caption || 'Foto do usuário'}
                      className="w-full h-24 object-cover rounded"
                    />
                    <button
                      className="absolute top-1 right-1 p-1 rounded bg-red-500 text-white opacity-0 group-hover:opacity-100 transition-opacity"
                      aria-label="Excluir foto"
                      onClick={() => handleDeletePhoto(photo.id)}
                      disabled={deletePhoto.isPending}
                    >
                      <Trash2 className="h-3 w-3" />
                    </button>
                    <div className="absolute bottom-0 left-0 right-0 bg-black/50 text-white text-xs p-1 rounded-b opacity-0 group-hover:opacity-100 transition-opacity">
                      {photo.is_approved ? '✅ Aprovada' : '⏳ Pendente'}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <Card className="p-8 text-center text-muted-foreground">
                <Camera className="h-12 w-12 mx-auto mb-2 opacity-50" />
                <p>Você ainda não enviou nenhuma foto</p>
                <p className="text-sm">Clique em "Enviar Foto" para começar</p>
              </Card>
            )}
          </section>
        )}

        {!user && (
          <section className="mt-6">
            <Card className="p-6 text-center">
              <LogIn className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
              <h3 className="font-semibold mb-2">Faça login para acessar todos os recursos</h3>
              <p className="text-sm text-muted-foreground mb-4">
                Envie fotos, salve favoritos e muito mais
              </p>
              <Button onClick={() => window.location.href = '/login'}>
                Fazer Login
              </Button>
            </Card>
          </section>
        )}
      </Container>
      <BottomNav />
    </div>
  );
}
