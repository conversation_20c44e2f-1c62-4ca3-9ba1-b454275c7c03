import React, { useMemo, useState } from 'react';
import Header from '@/components/layout/Header';
import Container from '@/components/layout/Container';
import BottomNav from '@/components/layout/BottomNav';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { useUserStore } from '@/stores/useUserStore';
import { usePhotoStore } from '@/stores/usePhotoStore';
import { Trash2, Camera, LogIn, UserPlus, Settings, Heart, Star, MapPin } from 'lucide-react';

export default function ProfilePage() {
  const { isLoggedIn, user, login, logout } = useUserStore();
  const { userPhotos, addUserPhoto, removeUserPhoto, toggleLike } = usePhotoStore();

  const [open, setOpen] = useState(false);
  const [file, setFile] = useState<File | null>(null);
  const [caption, setCaption] = useState('');

  const canSubmit = file && caption.trim().length >= 10 && caption.trim().length <= 200;

  const handleLogin = () =>
    login({ id: 'demo', name: 'Visitante', email: '<EMAIL>', member_since: 'Jan 2024' });

  const submitPhoto = () => {
    if (!file) return;
    const reader = new FileReader();
    reader.onload = () => {
      addUserPhoto({ image_url: reader.result as string, caption, user_name: user?.name || 'Visitante', likes_count: 0, is_liked: false });
      setOpen(false);
      setFile(null);
      setCaption('');
    };
    reader.readAsDataURL(file);
  };

  return (
    <div className="min-h-screen bg-background">
      <Header title="Perfil" />
      <Container>
        {/* Header */}
        <Card className="p-4 mb-4">
          <div className="flex items-center gap-3">
            <Avatar className="h-14 w-14">
              <AvatarImage src={user?.avatar_url || undefined} />
              <AvatarFallback>U</AvatarFallback>
            </Avatar>
            <div className="flex-1">
              <h2 className="font-semibold text-foreground">{isLoggedIn ? user?.name : 'Visitante'}</h2>
              <p className="text-sm text-muted-foreground">
                {isLoggedIn ? user?.email : 'Faça login para acessar todos os recursos'}
              </p>
            </div>
            {!isLoggedIn ? (
              <div className="flex gap-2">
                <Button onClick={handleLogin}><LogIn /> Entrar</Button>
                <Button variant="secondary"><UserPlus /> Criar Conta</Button>
              </div>
            ) : (
              <Button variant="secondary" onClick={logout}><Settings /> Sair</Button>
            )}
          </div>
          {isLoggedIn && (
            <div className="mt-3 text-sm text-muted-foreground">
              Membro desde: {user?.member_since || '—'} • 📸 {userPhotos.length} fotos • ⭐ 0 avaliações • ❤️ {(user?.favorites || []).length} favoritos
            </div>
          )}
        </Card>

        {/* Ações */}
        <div className="grid grid-cols-2 md:grid-cols-3 gap-3 mb-6">
          <Dialog open={open} onOpenChange={setOpen}>
            <DialogTrigger asChild>
              <Card role="button" tabIndex={0} className="p-4 text-center hover-scale">
                <div className="flex flex-col items-center">
                  <Camera />
                  <span className="mt-2 font-medium">Enviar Foto</span>
                </div>
              </Card>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Enviar Foto</DialogTitle>
              </DialogHeader>
              <div className="space-y-3">
                <label className="block text-sm">Arraste uma foto ou clique para selecionar</label>
                <input
                  type="file"
                  accept="image/jpeg,image/png,image/webp"
                  onChange={(e) => setFile(e.target.files?.[0] || null)}
                />
                {file && (
                  <img src={URL.createObjectURL(file)} alt="Pré-visualização" className="max-w-full max-h-[300px] object-contain rounded" />
                )}
                <div>
                  <label className="block text-sm mb-1">Descrição (obrigatório)</label>
                  <textarea
                    className="w-full border rounded p-2 text-sm"
                    placeholder="Descreva sua foto..."
                    value={caption}
                    onChange={(e) => setCaption(e.target.value)}
                    maxLength={200}
                  />
                  <div className="text-xs text-muted-foreground text-right">{caption.length}/200</div>
                </div>
                <div className="flex justify-end gap-2">
                  <Button variant="secondary" onClick={() => setOpen(false)}>Cancelar</Button>
                  <Button disabled={!canSubmit} onClick={submitPhoto}>Enviar Foto</Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>

          <Card className="p-4 text-center">
            <Star />
            <span className="mt-2 block font-medium">Minhas Avaliações</span>
          </Card>
          <Card className="p-4 text-center">
            <Heart />
            <span className="mt-2 block font-medium">Favoritos</span>
          </Card>
          <Card className="p-4 text-center">
            <MapPin />
            <span className="mt-2 block font-medium">Sugerir Local</span>
          </Card>
          <Card className="p-4 text-center">
            <Settings />
            <span className="mt-2 block font-medium">Configurações</span>
          </Card>
        </div>

        {/* Minhas fotos */}
        {isLoggedIn && userPhotos.length > 0 && (
          <section>
            <h3 className="text-lg font-semibold mb-3">Minhas Fotos</h3>
            <div className="grid grid-cols-3 gap-2">
              {userPhotos.map((p) => (
                <div key={p.id} className="relative group">
                  <img src={p.image_url} alt={p.caption} className="w-full h-24 object-cover rounded" />
                  <button
                    className="absolute top-1 right-1 p-1 rounded bg-white/80 hover:bg-white"
                    aria-label="Excluir foto"
                    onClick={() => removeUserPhoto(p.id)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </button>
                </div>
              ))}
            </div>
          </section>
        )}

        {!isLoggedIn && (
          <section className="mt-6 text-sm text-muted-foreground">
            <p className="font-medium mb-2">Recursos disponíveis sem login:</p>
            <ul className="list-disc pl-5 space-y-1">
              <li>Ver mapa</li>
              <li>Buscar locais</li>
              <li>Ver horários de transporte</li>
              <li>Contatar moto-táxi</li>
            </ul>
          </section>
        )}
      </Container>
      <BottomNav />
    </div>
  );
}
