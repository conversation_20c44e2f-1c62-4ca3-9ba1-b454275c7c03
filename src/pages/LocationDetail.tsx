import { useParams, useNavigate } from 'react-router-dom';
import { useEffect } from 'react';
import { ArrowLeft, MapPin, Clock, Info, Navigation } from 'lucide-react';
import Header from '@/components/layout/Header';
import BottomNav from '@/components/layout/BottomNav';
import Container from '@/components/layout/Container';
import { Button } from '@/components/ui/button';
import { beachLocations } from '@/data/beachLocations';

export default function LocationDetail() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  
  const location = beachLocations.find(beach => beach.id === id);
  
  useEffect(() => {
    if (location) {
      document.title = `${location.name} - Cotijuba`;
      const desc = location.description;
      let meta = document.querySelector('meta[name="description"]');
      if (!meta) {
        meta = document.createElement('meta');
        meta.setAttribute('name', 'description');
        document.head.appendChild(meta);
      }
      meta.setAttribute('content', desc);
    }
  }, [location]);

  if (!location) {
    return (
      <div className="min-h-screen bg-background">
        <Header title="Local não encontrado" />
        <Container>
          <div className="flex flex-col items-center justify-center min-h-[50vh] text-center">
            <h1 className="text-xl font-semibold mb-2">Local não encontrado</h1>
            <p className="text-muted-foreground mb-4">
              O local que você está procurando não existe ou foi removido.
            </p>
            <Button onClick={() => navigate('/mapa')}>
              Voltar ao Mapa
            </Button>
          </div>
        </Container>
        <BottomNav />
      </div>
    );
  }

  const openMaps = () => {
    const url = `https://www.openstreetmap.org/?mlat=${location.lat}&mlon=${location.lng}&zoom=16`;
    window.open(url, '_blank');
  };

  return (
    <div className="min-h-screen bg-background">
      <Header title={location.name} />
      
      {/* Back Button */}
      <div className="absolute top-4 left-4 z-50">
        <Button
          variant="secondary"
          size="sm"
          onClick={() => navigate('/mapa')}
          aria-label="Voltar ao mapa"
        >
          <ArrowLeft size={16} className="mr-1" />
          Voltar
        </Button>
      </div>
      
      <Container>
        <div className="space-y-6">
          {/* Hero Section */}
          <div className="bg-gradient-to-br from-blue-50 to-emerald-50 rounded-xl p-8 border border-blue-100">
            <h1 className="text-3xl font-bold text-slate-800 mb-4">{location.name}</h1>
            <p className="text-slate-600 text-lg leading-relaxed mb-6">{location.description}</p>
            <div className="flex items-center gap-3 text-base text-blue-600 bg-white/50 rounded-lg p-4">
              <MapPin size={20} />
              <span>{location.coordinates}</span>
            </div>
          </div>

          {/* Description */}
          <div className="bg-white rounded-xl border border-border p-8">
            <div className="flex items-center gap-3 mb-4">
              <Info size={24} className="text-blue-500" />
              <h2 className="text-2xl font-semibold">Sobre este local</h2>
            </div>
            <p className="text-muted-foreground text-lg leading-relaxed">
              {location.fullDescription}
            </p>
          </div>

          {/* How to Get There */}
          <div className="bg-white rounded-xl border border-border p-8">
            <div className="flex items-center gap-3 mb-4">
              <Navigation size={24} className="text-emerald-500" />
              <h2 className="text-2xl font-semibold">Como chegar</h2>
            </div>
            <p className="text-muted-foreground text-lg leading-relaxed">
              {location.howToGet}
            </p>
          </div>

          {/* Amenities */}
          <div className="bg-white rounded-xl border border-border p-8">
            <h2 className="text-2xl font-semibold mb-4">Comodidades</h2>
            <div className="flex flex-wrap gap-3">
              {location.amenities.map((amenity, i) => (
                <span 
                  key={i}
                  className="px-4 py-3 bg-blue-50 text-blue-700 text-base rounded-xl font-medium"
                >
                  {amenity}
                </span>
              ))}
            </div>
          </div>

          {/* Tips */}
          <div className="bg-white rounded-xl border border-border p-8">
            <div className="flex items-center gap-3 mb-4">
              <Clock size={24} className="text-orange-500" />
              <h2 className="text-2xl font-semibold">Dicas importantes</h2>
            </div>
            <ul className="space-y-4">
              {location.tips.map((tip, i) => (
                <li key={i} className="flex items-start gap-3 text-muted-foreground text-lg">
                  <span className="w-2 h-2 rounded-full bg-orange-500 mt-3 flex-shrink-0"></span>
                  <span className="leading-relaxed">{tip}</span>
                </li>
              ))}
            </ul>
          </div>

          {/* Actions */}
          <div className="flex gap-6 pb-8">
            <Button 
              onClick={openMaps}
              className="flex-1 py-4 text-lg"
              variant="default"
              size="lg"
            >
              <MapPin size={20} className="mr-3" />
              Ver no Mapa
            </Button>
            <Button 
              onClick={() => navigate('/moto-taxi')}
              variant="outline"
              className="flex-1 py-4 text-lg"
              size="lg"
            >
              <Navigation size={20} className="mr-3" />
              Moto-Táxi
            </Button>
          </div>
        </div>
      </Container>

      <BottomNav />
    </div>
  );
}