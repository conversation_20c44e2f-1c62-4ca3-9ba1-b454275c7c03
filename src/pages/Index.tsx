import Header from '@/components/layout/Header';
import Container from '@/components/layout/Container';
import BottomNav from '@/components/layout/BottomNav';
import WeatherWidget from '@/components/dashboard/WeatherWidget';
import TransportWidget from '@/components/dashboard/TransportWidget';
import TideWidget from '@/components/dashboard/TideWidget';
import QuickLinks from '@/components/dashboard/QuickLinks';
import PhotoCarousel from '@/components/features/photos/PhotoCarousel';
import EventsWidget from '@/components/dashboard/EventsWidget';

const Index = () => {
  return (
    <div className="min-h-screen bg-background">
      <Header title="cotiju.app" />
      
      <Container>
        {/* Saudação personalizada */}
        <div className="mb-6 sm:mb-8 px-1">
          <h2 className="text-xl sm:text-2xl md:text-3xl lg:text-4xl font-bold text-foreground mb-2">
            Olá! 👋
          </h2>
          <p className="text-sm sm:text-base md:text-lg text-muted-foreground leading-relaxed">
            Sua companhia completa para explorar Cotijuba
          </p>
        </div>

        {/* Carrossel de fotos no topo */}
        <PhotoCarousel />

        {/* Widgets informativos responsivos */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-8">
          <WeatherWidget />
          <TransportWidget />
          <TideWidget />
          <EventsWidget />
        </div>

        {/* Links de acesso rápido */}
        <QuickLinks />

        {/* Mensagem de boas-vindas */}
        <div className="mt-8 p-4 sm:p-6 md:p-8 rounded-xl bg-gradient-to-r from-blue-50 to-emerald-50 border border-blue-100">
          <div className="text-center">
            <h3 className="font-semibold text-slate-800 mb-2 sm:text-lg md:text-xl">
              Bem-vindo à Cotijuba! 🏝️
            </h3>
            <p className="text-sm sm:text-base text-slate-600 max-w-2xl mx-auto">
              Descubra as praias paradisíacas, a cultura local e todas as aventuras que nossa ilha tem a oferecer.
            </p>
          </div>
        </div>
      </Container>

      <BottomNav />
    </div>
  );
};

export default Index;
