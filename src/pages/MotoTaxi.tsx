import React, { useMemo, useState } from 'react';
import Header from '@/components/layout/Header';
import Container from '@/components/layout/Container';
import BottomNav from '@/components/layout/BottomNav';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import { Skeleton } from '@/components/ui/skeleton';
import { Search, Filter, PhoneCall, MessageCircle, Clock, BadgeCheck, Star, Circle } from 'lucide-react';
import { useMotoTaxiStore, type Driver } from '@/stores/useMotoTaxiStore';
import { useDriversRealtime } from '@/hooks/useMotoTaxi';

const formatRating = (rating: number, total: number) => {
  if (total === 0 || rating === 0) return 'Novo';
  return `${rating.toFixed(1)} (${total})`;
};

function DriverCard({ d }: { d: Driver }) {
  return (
    <Card className="p-4">
      <div className="flex items-start gap-3">
        <Avatar className="h-12 w-12">
          <AvatarImage src={d.photo_url || undefined} alt={d.name} />
          <AvatarFallback>{d.name.slice(0,2).toUpperCase()}</AvatarFallback>
        </Avatar>
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2">
            <h3 className="font-semibold text-foreground truncate">{d.name}</h3>
            {d.is_available && (
              <span className="flex items-center text-xs text-emerald-600">
                <Circle className="h-2 w-2 fill-emerald-500 text-emerald-500 mr-1" />
                Online agora
              </span>
            )}
          </div>
          <div className="flex items-center gap-2 mt-1">
            <Badge variant="secondary">{d.vehicle_type === 'motorcycle' ? 'Moto' : 'Motorrete'}</Badge>
            {d.total_reviews > 0 ? (
              <span className="text-xs text-muted-foreground flex items-center gap-1">
                <Star className="h-3 w-3 text-yellow-500" /> {formatRating(d.rating, d.total_reviews)}
              </span>
            ) : (
              <Badge className="text-xs">Novo</Badge>
            )}
          </div>
          <div className="mt-2 grid grid-cols-2 gap-2 text-xs text-muted-foreground">
            <div>Experiência: <span className="font-medium text-foreground">{d.years_experience} anos</span></div>
            <div>Preço: <span className="font-medium text-foreground">{d.price_range}</span></div>
            <div className="col-span-2">Áreas: {d.areas_covered.map((a) => (
              <span key={a} className="inline-block text-xs px-2 py-0.5 bg-muted rounded-full mr-1 mt-1">{a}</span>
            ))}</div>
            <div>Horário: <span className="font-medium text-foreground">{d.available_hours}</span></div>
          </div>
          <div className="mt-3 flex items-center gap-2">
            {d.accepts_pix && <Badge variant="secondary">PIX</Badge>}
            {d.accepts_cash && <Badge variant="secondary">Dinheiro</Badge>}
            {d.languages?.length ? (
              <span className="text-xs text-muted-foreground">Idiomas: {d.languages.join(', ')}</span>
            ) : null}
          </div>
          <div className="mt-4 grid grid-cols-2 gap-2">
            <Button asChild className="min-h-[44px]">
              <a href={`https://wa.me/55${d.whatsapp.replace(/\D/g,'')}`} target="_blank" rel="noreferrer" aria-label={`WhatsApp para ${d.name}`}>
                <MessageCircle /> WhatsApp
              </a>
            </Button>
            <Button variant="secondary" asChild className="min-h-[44px]">
              <a href={`tel:+55${d.phone.replace(/\D/g,'')}`} aria-label={`Ligar para ${d.name}`}>
                <PhoneCall /> Ligar
              </a>
            </Button>
          </div>
        </div>
      </div>
    </Card>
  );
}

export default function MotoTaxiPage() {
  const { filters, setFilter } = useMotoTaxiStore();
  const [tab, setTab] = useState<'motorcycle' | 'motorrete'>('motorcycle');
  const [search, setSearch] = useState('');

  // Debounce search
  React.useEffect(() => {
    const t = setTimeout(() => setFilter('searchQuery', search), 300);
    return () => clearTimeout(t);
  }, [search, setFilter]);

  // Create filter object for the query
  const queryFilters = useMemo(() => ({
    vehicleType: tab,
    availableNow: filters.availableNow,
    acceptsPix: filters.acceptsPix,
    speaksEnglish: filters.speaksEnglish,
    searchQuery: filters.searchQuery,
  }), [tab, filters]);

  // Use the real-time drivers hook
  const { data: drivers = [], isLoading, error } = useDriversRealtime(queryFilters);

  // Get counts for all drivers (separate queries for counts)
  const { data: allMotorcycleDrivers = [] } = useDriversRealtime({ vehicleType: 'motorcycle' });
  const { data: allMotorreteDrivers = [] } = useDriversRealtime({ vehicleType: 'motorrete' });

  const counts = useMemo(() => ({
    moto: allMotorcycleDrivers.length,
    motorrete: allMotorreteDrivers.length,
  }), [allMotorcycleDrivers, allMotorreteDrivers]);

  return (
    <div className="min-h-screen bg-background">
      <Header title="Moto-Táxi" />
      <Container>
        {/* Cabeçalho */}
        <div className="widget-card mb-4">
          <h1 className="text-xl font-semibold mb-1">Moto-Táxi em Cotijuba</h1>
          <p className="text-sm text-muted-foreground">Transporte rápido e seguro pela ilha</p>
          <div className="mt-3 flex items-center gap-2 text-sm">
            <BadgeCheck className="h-4 w-4 text-primary" />
            <span>🏍️ Único meio de transporte terrestre na ilha</span>
          </div>
        </div>

        {/* Filtros e busca */}
        <div className="grid grid-cols-1 gap-3 mb-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <input
              type="text"
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              placeholder="Buscar por nome ou área..."
              className="w-full pl-9 pr-3 py-2 rounded-md border bg-background text-sm"
              aria-label="Buscar motoristas"
            />
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant={filters.availableNow ? 'default' : 'outline'}
              size="sm"
              onClick={() => setFilter('availableNow', !filters.availableNow)}
            >
              Disponível agora
            </Button>
            <Button
              variant={filters.acceptsPix ? 'default' : 'outline'}
              size="sm"
              onClick={() => setFilter('acceptsPix', !filters.acceptsPix)}
            >
              Aceita PIX
            </Button>
            <Button
              variant={filters.speaksEnglish ? 'default' : 'outline'}
              size="sm"
              onClick={() => setFilter('speaksEnglish', !filters.speaksEnglish)}
            >
              Fala inglês
            </Button>
          </div>
        </div>

        {/* Abas por categoria */}
        <Tabs value={tab} onValueChange={(v) => setTab(v as any)} className="mb-4">
          <TabsList className="grid grid-cols-2 w-full">
            <TabsTrigger value="motorcycle">
              Moto (<span className="ml-1 text-primary">{counts.moto}</span>)
            </TabsTrigger>
            <TabsTrigger value="motorrete">
              Motorrete (<span className="ml-1 text-primary">{counts.motorrete}</span>)
            </TabsTrigger>
          </TabsList>

          <TabsContent value="motorcycle" className="space-y-3 mt-3">
            {isLoading ? (
              <div className="space-y-3">
                {[...Array(3)].map((_, i) => (
                  <Card key={i} className="p-4">
                    <div className="flex items-start gap-3">
                      <Skeleton className="h-12 w-12 rounded-full" />
                      <div className="flex-1 space-y-2">
                        <Skeleton className="h-4 w-3/4" />
                        <Skeleton className="h-3 w-1/2" />
                        <Skeleton className="h-3 w-full" />
                        <div className="flex gap-2">
                          <Skeleton className="h-8 w-20" />
                          <Skeleton className="h-8 w-20" />
                        </div>
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            ) : error ? (
              <Card className="p-4 text-center text-sm text-red-600">
                Erro ao carregar motoristas. Tente novamente.
              </Card>
            ) : drivers.length ? (
              drivers.map((d) => <DriverCard key={d.id} d={d} />)
            ) : (
              <Card className="p-4 text-center text-sm text-muted-foreground">
                Nenhum motorista disponível. Tente ajustar os filtros.
              </Card>
            )}
          </TabsContent>

          <TabsContent value="motorrete" className="space-y-3 mt-3">
            {isLoading ? (
              <div className="space-y-3">
                {[...Array(3)].map((_, i) => (
                  <Card key={i} className="p-4">
                    <div className="flex items-start gap-3">
                      <Skeleton className="h-12 w-12 rounded-full" />
                      <div className="flex-1 space-y-2">
                        <Skeleton className="h-4 w-3/4" />
                        <Skeleton className="h-3 w-1/2" />
                        <Skeleton className="h-3 w-full" />
                        <div className="flex gap-2">
                          <Skeleton className="h-8 w-20" />
                          <Skeleton className="h-8 w-20" />
                        </div>
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            ) : error ? (
              <Card className="p-4 text-center text-sm text-red-600">
                Erro ao carregar motoristas. Tente novamente.
              </Card>
            ) : drivers.length ? (
              drivers.map((d) => <DriverCard key={d.id} d={d} />)
            ) : (
              <Card className="p-4 text-center text-sm text-muted-foreground">
                Nenhum motorista disponível. Tente ajustar os filtros.
              </Card>
            )}
          </TabsContent>
        </Tabs>
      </Container>
      <BottomNav />
    </div>
  );
}
