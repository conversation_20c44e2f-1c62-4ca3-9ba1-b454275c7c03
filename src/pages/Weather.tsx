import { Cloud, Sun, CloudRain, Wind, Droplets, Eye, Thermometer } from 'lucide-react';
import Header from '@/components/layout/Header';
import Container from '@/components/layout/Container';
import BottomNav from '@/components/layout/BottomNav';
import { Badge } from '@/components/ui/badge';

// Mock weather data - will be replaced with real API
const weatherData = {
  current: {
    temperature: 28,
    feelsLike: 32,
    condition: 'Parcialmente nublado',
    humidity: 75,
    windSpeed: 15,
    visibility: 10,
    icon: Cloud
  },
  daily: [
    { day: 'Hoje', min: 24, max: 30, condition: 'Nublado', icon: Cloud },
    { day: 'Amanhã', min: 23, max: 29, condition: 'Chuva', icon: CloudRain },
    { day: 'Ter', min: 25, max: 31, condition: 'Sol', icon: Sun },
    { day: 'Qua', min: 26, max: 32, condition: 'Sol', icon: Sun },
    { day: 'Qui', min: 24, max: 29, condition: 'Nublado', icon: Cloud },
    { day: 'Sex', min: 25, max: 30, condition: 'Sol', icon: Sun },
    { day: 'Sáb', min: 26, max: 31, condition: 'Sol', icon: Sun }
  ],
  hourly: [
    { time: '14:00', temp: 28, condition: 'Nublado', icon: Cloud },
    { time: '15:00', temp: 29, condition: 'Nublado', icon: Cloud },
    { time: '16:00', temp: 30, condition: 'Sol', icon: Sun },
    { time: '17:00', temp: 29, condition: 'Sol', icon: Sun },
    { time: '18:00', temp: 27, condition: 'Sol', icon: Sun },
    { time: '19:00', temp: 26, condition: 'Nublado', icon: Cloud }
  ],
  beachCondition: 'good' // 'excellent', 'good', 'fair', 'poor'
};

export default function Weather() {
  const { current, daily, hourly, beachCondition } = weatherData;
  const CurrentIcon = current.icon;

  const getBeachConditionBadge = (condition: string) => {
    switch (condition) {
      case 'excellent':
        return <Badge className="bg-emerald-500 text-white">Excelente para praia</Badge>;
      case 'good':
        return <Badge className="bg-blue-500 text-white">Bom para praia</Badge>;
      case 'fair':
        return <Badge variant="secondary">Regular para praia</Badge>;
      case 'poor':
        return <Badge variant="destructive">Evitar praia</Badge>;
      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <Header title="Clima" />
      
      <Container>
        {/* Condições atuais */}
        <div className="widget-card mb-6">
          <div className="text-center">
            <div className="flex justify-center mb-4">
              <div className="p-4 rounded-full bg-gradient-to-br from-blue-500 to-blue-600 text-white">
                <CurrentIcon size={48} />
              </div>
            </div>
            <h2 className="text-3xl font-bold text-foreground mb-2">{current.temperature}°C</h2>
            <p className="text-muted-foreground mb-4">{current.condition}</p>
            
            <div className="grid grid-cols-2 gap-4 pt-4 border-t border-slate-100">
              <div className="text-center">
                <div className="flex items-center justify-center gap-1 text-muted-foreground mb-1">
                  <Thermometer size={14} />
                  <span className="text-xs">Sensação</span>
                </div>
                <span className="font-semibold">{current.feelsLike}°C</span>
              </div>
              <div className="text-center">
                <div className="flex items-center justify-center gap-1 text-muted-foreground mb-1">
                  <Droplets size={14} />
                  <span className="text-xs">Umidade</span>
                </div>
                <span className="font-semibold">{current.humidity}%</span>
              </div>
              <div className="text-center">
                <div className="flex items-center justify-center gap-1 text-muted-foreground mb-1">
                  <Wind size={14} />
                  <span className="text-xs">Vento</span>
                </div>
                <span className="font-semibold">{current.windSpeed} km/h</span>
              </div>
              <div className="text-center">
                <div className="flex items-center justify-center gap-1 text-muted-foreground mb-1">
                  <Eye size={14} />
                  <span className="text-xs">Visibilidade</span>
                </div>
                <span className="font-semibold">{current.visibility} km</span>
              </div>
            </div>
          </div>
        </div>

        {/* Condições da praia */}
        <div className="widget-card mb-6">
          <div className="flex items-center justify-between">
            <h3 className="font-semibold text-foreground">Condições da Praia</h3>
            {getBeachConditionBadge(beachCondition)}
          </div>
          <p className="text-sm text-muted-foreground mt-2">
            Baseado na temperatura, vento e condições gerais do tempo
          </p>
        </div>

        {/* Previsão hoje (horária) */}
        <div className="widget-card mb-6">
          <h3 className="font-semibold text-foreground mb-4">Hoje</h3>
          <div className="flex gap-4 overflow-x-auto pb-2">
            {hourly.map((hour, index) => {
              const HourIcon = hour.icon;
              return (
                <div key={index} className="flex-shrink-0 text-center p-3 rounded-lg bg-slate-50 min-w-[80px]">
                  <p className="text-xs text-muted-foreground mb-2">{hour.time}</p>
                  <div className="flex justify-center mb-2">
                    <HourIcon size={24} className="text-slate-600" />
                  </div>
                  <p className="font-semibold text-sm">{hour.temp}°</p>
                </div>
              );
            })}
          </div>
        </div>

        {/* Previsão semanal */}
        <div className="widget-card">
          <h3 className="font-semibold text-foreground mb-4">Próximos 7 dias</h3>
          <div className="space-y-3">
            {daily.map((day, index) => {
              const DayIcon = day.icon;
              return (
                <div key={index} className="flex items-center justify-between py-2">
                  <div className="flex items-center gap-3">
                    <DayIcon size={20} className="text-slate-600" />
                    <div>
                      <span className="font-medium text-foreground">{day.day}</span>
                      <p className="text-sm text-muted-foreground">{day.condition}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <span className="font-semibold text-foreground">{day.max}°</span>
                    <span className="text-muted-foreground ml-2">{day.min}°</span>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </Container>

      <BottomNav />
    </div>
  );
}