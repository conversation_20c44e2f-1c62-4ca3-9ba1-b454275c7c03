import { useState } from 'react';
import { Ship, Clock, MapPin, CreditCard, Zap } from 'lucide-react';
import Header from '@/components/layout/Header';
import Container from '@/components/layout/Container';
import BottomNav from '@/components/layout/BottomNav';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

// Mock transport data - will be replaced with Supabase
const transportSchedules = {
  today: [
    { time: '06:00', type: 'Municipal', duration: 40, price: 4.60, status: 'departed' },
    { time: '09:00', type: 'Municipal', duration: 40, price: 4.60, status: 'departed' },
    { time: '12:00', type: 'Municipal', duration: 40, price: 4.60, status: 'departed' },
    { time: '15:00', type: 'Municipal', duration: 40, price: 4.60, status: 'scheduled' },
    { time: '16:00', type: 'Geladão', duration: 20, price: 4.60, status: 'scheduled', highlight: true },
    { time: '18:30', type: 'Municipal', duration: 40, price: 9.20, status: 'scheduled' }
  ],
  prices: {
    municipal_weekday: 4.60,
    municipal_weekend: 9.20,
    gelado_weekday: 4.60,
    gelado_weekend: 9.20,
    cooperativa: 10.00
  }
};

const tabs = ['Hoje', 'Semana', 'Tarifas'];

export default function Transport() {
  const [activeTab, setActiveTab] = useState('Hoje');

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'departed':
        return <Badge variant="secondary" className="text-xs">Partiu</Badge>;
      case 'scheduled':
        return <Badge variant="outline" className="text-xs text-emerald-600 border-emerald-200">Programado</Badge>;
      default:
        return null;
    }
  };

  const renderTodaySchedule = () => (
    <div className="space-y-3">
      {/* Detecção de localização */}
      <div className="flex items-center gap-2 p-3 bg-blue-50 rounded-lg border border-blue-100">
        <MapPin size={16} className="text-blue-600" />
        <span className="text-sm text-blue-800">
          Mostrando barcos de <strong>Icoaraci para Cotijuba</strong>
        </span>
      </div>

      {transportSchedules.today.map((schedule, index) => (
        <div 
          key={index}
          className={`widget-card ${schedule.highlight ? 'border-orange-200 bg-orange-50' : ''}`}
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-lg bg-gradient-to-br from-emerald-600 to-emerald-700 text-white">
                <Ship size={20} />
              </div>
              <div>
                <h3 className="font-semibold text-foreground">{schedule.time}</h3>
                <div className="flex items-center gap-2">
                  <span className="text-sm text-muted-foreground">{schedule.type}</span>
                  {schedule.highlight && (
                    <div className="flex items-center gap-1 text-xs font-medium text-orange-600">
                      <Zap size={12} />
                      <span>RÁPIDO - {schedule.duration} min</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
            
            <div className="text-right">
              <div className="text-lg font-bold text-foreground">R$ {schedule.price.toFixed(2)}</div>
              <div className="flex items-center gap-1 text-sm text-muted-foreground">
                <Clock size={12} />
                <span>{schedule.duration}min</span>
              </div>
            </div>
          </div>
          
          <div className="flex items-center justify-between pt-2 border-t border-slate-100">
            {getStatusBadge(schedule.status)}
            <div className="flex items-center gap-2">
              <CreditCard size={14} className="text-muted-foreground" />
              <span className="text-xs text-muted-foreground">Dinheiro • PIX</span>
            </div>
          </div>
        </div>
      ))}
    </div>
  );

  const renderWeekSchedule = () => (
    <div className="text-center py-8">
      <h3 className="text-lg font-semibold text-foreground mb-2">Horários da Semana</h3>
      <p className="text-muted-foreground mb-4">
        Horários regulares de segunda a domingo
      </p>
      <div className="space-y-4">
        <div className="p-4 bg-slate-50 rounded-lg">
          <h4 className="font-medium text-foreground mb-2">Municipal</h4>
          <p className="text-sm text-muted-foreground">
            06:00 • 09:00 • 12:00 • 15:00 • 18:30
          </p>
        </div>
        <div className="p-4 bg-slate-50 rounded-lg">
          <h4 className="font-medium text-foreground mb-2">Geladão Fluvial</h4>
          <p className="text-sm text-muted-foreground">
            A cada 2 horas, das 06:00 às 18:00
          </p>
        </div>
        <div className="p-4 bg-slate-50 rounded-lg">
          <h4 className="font-medium text-foreground mb-2">Cooperativa</h4>
          <p className="text-sm text-muted-foreground">
            Saída conforme lotação
          </p>
        </div>
      </div>
    </div>
  );

  const renderPrices = () => (
    <div className="space-y-4">
      <div className="widget-card">
        <h3 className="font-semibold text-foreground mb-3">Municipal & Geladão</h3>
        <div className="space-y-2">
          <div className="flex justify-between">
            <span className="text-muted-foreground">Segunda a Sexta</span>
            <span className="font-medium">R$ 4,60</span>
          </div>
          <div className="flex justify-between">
            <span className="text-muted-foreground">Fim de Semana</span>
            <span className="font-medium">R$ 9,20</span>
          </div>
        </div>
      </div>
      
      <div className="widget-card">
        <h3 className="font-semibold text-foreground mb-3">Cooperativa</h3>
        <div className="space-y-2">
          <div className="flex justify-between">
            <span className="text-muted-foreground">Todos os dias</span>
            <span className="font-medium">R$ 10,00</span>
          </div>
          <div className="text-sm text-muted-foreground">
            * Duração aproximada: 40 minutos
          </div>
        </div>
      </div>

      <div className="p-4 bg-amber-50 rounded-lg border border-amber-200">
        <p className="text-sm text-amber-800">
          <strong>Dica:</strong> O Geladão é mais rápido (20 min) e tem ar condicionado!
        </p>
      </div>
    </div>
  );

  const renderTabContent = () => {
    switch (activeTab) {
      case 'Hoje':
        return renderTodaySchedule();
      case 'Semana':
        return renderWeekSchedule();
      case 'Tarifas':
        return renderPrices();
      default:
        return renderTodaySchedule();
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <Header title="Transporte" />
      
      <Container>
        {/* Tab Navigation */}
        <div className="flex bg-slate-100 rounded-lg p-1 mb-6">
          {tabs.map((tab) => (
            <Button
              key={tab}
              variant={activeTab === tab ? "default" : "ghost"}
              size="sm"
              className={`flex-1 ${activeTab === tab ? 'btn-ocean' : 'text-muted-foreground hover:text-foreground'}`}
              onClick={() => setActiveTab(tab)}
            >
              {tab}
            </Button>
          ))}
        </div>

        {/* Tab Content */}
        {renderTabContent()}
      </Container>

      <BottomNav />
    </div>
  );
}