
import { MapPin, Navigation, Waves, Map as MapIcon, List } from 'lucide-react';
import { useEffect, useState, useMemo } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, TileLayer, <PERSON>er, Popup } from 'react-leaflet';
import Header from '@/components/layout/Header';
import BottomNav from '@/components/layout/BottomNav';
import Container from '@/components/layout/Container';
import { Link, useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { beachLocations } from '@/data/beachLocations';
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';

// Fix default markers
delete (L.Icon.Default.prototype as any)._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
});

// Interactive Leaflet map component
function InteractiveMap({ userLat, userLng, onMarkerClick }: {
  userLat?: number;
  userLng?: number;
  onMarkerClick: (locationId: string) => void;
}) {
  const cotijubaCenter: [number, number] = [-1.2111, -48.6208];

  const userIcon = useMemo(() => new L.Icon({
    iconUrl: 'https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon.png',
    iconRetinaUrl: 'https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon-2x.png',
    iconSize: [25, 41],
    iconAnchor: [12, 41],
    popupAnchor: [1, -34],
    shadowUrl: 'https://unpkg.com/leaflet@1.7.1/dist/images/marker-shadow.png',
    shadowSize: [41, 41],
    className: 'bg-blue-500'
  }), []);

  return (
    <div className="w-full h-full map-container">
      <MapContainer
        center={cotijubaCenter}
        zoom={14}
        style={{ height: '100%', width: '100%' }}
        scrollWheelZoom={true}
        className="map-container"
      >
        <TileLayer
          url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
          attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        />
        {beachLocations.map(location => (
          <Marker
            key={location.id}
            position={[location.lat, location.lng]}
            eventHandlers={{
              click: () => onMarkerClick(location.id)
            }}
          >
            <Popup>{location.name}</Popup>
          </Marker>
        ))}
        {userLat && userLng && (
          <Marker position={[userLat, userLng]} icon={userIcon}>
            <Popup>Sua localização</Popup>
          </Marker>
        )}
      </MapContainer>
    </div>
  );
}

// User location component
function UserLocationButton({ onLocate }: { onLocate: (pos: { lat: number; lng: number }) => void }) {
  const [isLoading, setIsLoading] = useState(false);

  const handleGetLocation = () => {
    if (navigator.geolocation) {
      setIsLoading(true);
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const { latitude, longitude } = position.coords;
          console.log('Localização obtida:', latitude, longitude);
          onLocate({ lat: latitude, lng: longitude });
          setIsLoading(false);
        },
        (error) => {
          console.log('Erro ao obter localização:', error);
          setIsLoading(false);
        }
      );
    }
  };

  return (
    <div className="absolute top-20 right-4 z-[100] md:top-4">
      <Button
        size="sm"
        variant="secondary"
        onClick={handleGetLocation}
        disabled={isLoading}
        aria-label="Usar minha localização"
        className="shadow-lg"
      >
        <Navigation size={16} className="mr-1" />
        <span className="hidden sm:inline">{isLoading ? 'Localizando...' : 'Localizar-me'}</span>
        <span className="sm:hidden">{isLoading ? '...' : 'GPS'}</span>
      </Button>
    </div>
  );
}



function Map() {
  const [viewMode, setViewMode] = useState<'map' | 'list'>('map');
  const [userPosition, setUserPosition] = useState<{ lat: number; lng: number } | null>(null);
  const navigate = useNavigate();

  const handleMarkerClick = (locationId: string) => {
    navigate(`/locais/${locationId}`);
  };
  
  // SEO
  useEffect(() => {
    document.title = 'Mapa de Cotijuba - Praias e Pontos';
    const desc = 'Mapa interativo de Cotijuba com praias e pontos de interesse.';
    let meta = document.querySelector('meta[name="description"]');
    if (!meta) {
      meta = document.createElement('meta');
      meta.setAttribute('name', 'description');
      document.head.appendChild(meta);
    }
    meta.setAttribute('content', desc);
  }, []);

  return (
    <div className="flex flex-col h-screen bg-background">
      <Header title="Mapa da Ilha" />

      {/* View Toggle */}
      <div className="flex-shrink-0 px-4 py-3 bg-white/95 backdrop-blur-sm border-b border-slate-200/50">
        <div className="flex justify-between items-center">
          <h2 className="text-lg font-semibold text-foreground flex items-center gap-2">
            <Waves size={20} className="text-blue-500" />
            <span className="hidden sm:inline">Praias de Cotijuba</span>
            <span className="sm:hidden">Praias</span>
          </h2>
          <div className="flex gap-2">
            <Button
              variant={viewMode === 'map' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setViewMode('map')}
            >
              <MapIcon size={16} className="mr-1" />
              <span className="hidden sm:inline">Mapa</span>
              <span className="sm:hidden">Map</span>
            </Button>
            <Button
              variant={viewMode === 'list' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setViewMode('list')}
            >
              <List size={16} className="mr-1" />
              <span className="hidden sm:inline">Lista</span>
              <span className="sm:hidden">List</span>
            </Button>
          </div>
        </div>
      </div>

      {/* Map View */}
      {viewMode === 'map' && (
        <div className="flex-1 relative overflow-hidden" style={{ zIndex: 1 }}>
          <InteractiveMap
            userLat={userPosition?.lat}
            userLng={userPosition?.lng}
            onMarkerClick={handleMarkerClick}
          />
          <UserLocationButton onLocate={(pos) => setUserPosition(pos)} />
        </div>
      )}

      {/* List View */}
      {viewMode === 'list' && (
        <div className="flex-1 overflow-y-auto" style={{ zIndex: 1 }}>
          <Container className="py-6 pb-24">
            <div className="space-y-6">
            {beachLocations.map((beach, index) => (
              <div 
                key={beach.id} 
                className="bg-white rounded-xl shadow-sm border border-gray-100 cursor-pointer hover:shadow-md transition-shadow"
                onClick={() => handleMarkerClick(beach.id)}
              >
                {/* Header */}
                <div className="p-5 border-b border-gray-50">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <div className="w-8 h-8 bg-primary text-white rounded-full flex items-center justify-center text-sm font-bold">
                          {index + 1}
                        </div>
                        <h3 className="text-lg font-semibold text-foreground">{beach.name}</h3>
                      </div>
                      <p className="text-muted-foreground mb-3">{beach.description}</p>
                      <div className="flex items-center gap-1 text-sm text-blue-600">
                        <MapPin size={14} />
                        <span>{beach.coordinates}</span>
                      </div>
                    </div>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleMarkerClick(beach.id);
                      }}
                      aria-label={`Ver detalhes de ${beach.name}`}
                    >
                      Ver Detalhes
                    </Button>
                  </div>
                </div>
                
                {/* Amenities */}
                <div className="p-5">
                  <p className="text-sm font-medium text-muted-foreground mb-3">Comodidades disponíveis:</p>
                  <div className="flex flex-wrap gap-2">
                    {beach.amenities.map((amenity, i) => (
                      <span 
                        key={i}
                        className="px-3 py-1.5 bg-blue-50 text-blue-700 text-sm rounded-full font-medium"
                      >
                        {amenity}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Navigation tip */}
          <div className="mt-8 p-6 bg-gradient-to-r from-blue-50 to-emerald-50 rounded-xl border border-blue-100">
            <div className="flex items-start gap-3">
              <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                💡
              </div>
              <div className="flex-1">
                <h3 className="font-semibold text-slate-800 mb-2">
                  Dica de Navegação
                </h3>
                <p className="text-sm text-slate-600 mb-4">
                  Use o mapa interativo para encontrar a melhor rota até cada praia e descobrir pontos de interesse próximos. Todos os marcadores no mapa são clicáveis!
                </p>
                <div className="flex gap-3">
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => setViewMode('map')}
                  >
                    <MapIcon size={14} className="mr-1" />
                    Ver Mapa
                  </Button>
                  <Link to="/moto-taxi">
                    <Button size="sm">
                      <Navigation size={14} className="mr-1" />
                      Moto-Táxi
                    </Button>
                  </Link>
                </div>
              </div>
            </div>
            </div>
          </Container>
        </div>
      )}

      {/* FAB */}
      <div className="fixed bottom-24 right-4 z-[200] md:bottom-6">
        <div className="relative group">
          <button className="h-12 w-12 rounded-full bg-primary text-primary-foreground shadow-lg hover:shadow-xl transition-shadow">
            +
          </button>
          <div className="absolute bottom-14 right-0 hidden group-hover:block bg-background border rounded-md shadow-lg p-2 w-48">
            <Link to="/suggest" className="block px-2 py-2 hover:bg-muted rounded text-sm">
              Sugerir novo local
            </Link>
            <Link to="/suggest?type=report" className="block px-2 py-2 hover:bg-muted rounded text-sm">
              Reportar problema
            </Link>
          </div>
        </div>
      </div>

      <BottomNav />
    </div>
  );
}

export default Map;
